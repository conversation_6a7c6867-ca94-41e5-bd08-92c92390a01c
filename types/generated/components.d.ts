import type { Schema, Struct } from '@strapi/strapi';

export interface AboutAbout extends Struct.ComponentSchema {
  collectionName: 'components_about_abouts';
  info: {
    description: '';
    displayName: 'About';
    icon: 'question';
  };
  attributes: {
    about: Schema.Attribute.Text;
    facebook: Schema.Attribute.String;
    instagram: Schema.Attribute.String;
    privacy: Schema.Attribute.Text;
    tiktok: Schema.Attribute.String;
    whatsapp: Schema.Attribute.String;
    youtube: Schema.Attribute.String;
  };
}

export interface AddressAddress extends Struct.ComponentSchema {
  collectionName: 'components_address_addresses';
  info: {
    displayName: 'Address';
    icon: 'stack';
  };
  attributes: {
    apartment: Schema.Attribute.String;
    building: Schema.Attribute.String;
    city: Schema.Attribute.String;
    floor: Schema.Attribute.String;
    state: Schema.Attribute.String;
    street_name: Schema.Attribute.String;
  };
}

export interface AreasAreas extends Struct.ComponentSchema {
  collectionName: 'components_areas_areas';
  info: {
    description: '';
    displayName: 'Areas';
    icon: 'bulletList';
  };
  attributes: {
    cost: Schema.Attribute.Decimal;
    free_shipping: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    is_active: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<true>;
    name: Schema.Attribute.String;
    name_ar: Schema.Attribute.String;
  };
}

export interface BoundariesBoundaries extends Struct.ComponentSchema {
  collectionName: 'components_boundaries_boundaries';
  info: {
    displayName: 'boundaries';
    icon: 'pinMap';
  };
  attributes: {
    lat: Schema.Attribute.String;
    lng: Schema.Attribute.String;
  };
}

export interface CitiesCostCitiesCost extends Struct.ComponentSchema {
  collectionName: 'components_cities_cost_cities_costs';
  info: {
    description: '';
    displayName: 'Cities Cost';
  };
  attributes: {
    areas: Schema.Attribute.Component<'areas.areas', true>;
    city: Schema.Attribute.Relation<'oneToOne', 'api::city.city'>;
    cost: Schema.Attribute.Decimal;
    free_shipping: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    is_active: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<true>;
  };
}

export interface ContactUsContactUs extends Struct.ComponentSchema {
  collectionName: 'components_contact_us_contact_uses';
  info: {
    description: '';
    displayName: 'Contact Us';
    icon: 'phone';
  };
  attributes: {
    email: Schema.Attribute.Component<'contact-us.contact-us-field', false>;
    facebook: Schema.Attribute.Component<'contact-us.contact-us-field', false>;
    instagram: Schema.Attribute.Component<'contact-us.contact-us-field', false>;
    tiktok: Schema.Attribute.Component<'contact-us.contact-us-field', false>;
    website: Schema.Attribute.Component<'contact-us.contact-us-field', false>;
    whatsapp: Schema.Attribute.Component<'contact-us.contact-us-field', false>;
    youtube: Schema.Attribute.Component<'contact-us.contact-us-field', false>;
  };
}

export interface ContactUsContactUsField extends Struct.ComponentSchema {
  collectionName: 'components_contact_us_contact_us_fields';
  info: {
    displayName: 'Contact Us Field';
    icon: 'file';
  };
  attributes: {
    name: Schema.Attribute.String;
    url: Schema.Attribute.String;
  };
}

export interface ExtraSettingsExtraSettings extends Struct.ComponentSchema {
  collectionName: 'components_extra_settings_extra_settings';
  info: {
    description: '';
    displayName: 'Extra Settings';
    icon: 'magic';
  };
  attributes: {
    categories: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-category.product-category'
    >;
    name: Schema.Attribute.String;
    name_ar: Schema.Attribute.String;
  };
}

export interface ExtraSettingsProductExtraSettings
  extends Struct.ComponentSchema {
  collectionName: 'components_extra_settings_product_extra_settings';
  info: {
    description: '';
    displayName: 'Product Extra Settings';
    icon: 'feather';
  };
  attributes: {
    name: Schema.Attribute.String;
    name_ar: Schema.Attribute.String;
    price: Schema.Attribute.Decimal;
    stock: Schema.Attribute.String;
  };
}

export interface ExtraSettingsProductsQuantity extends Struct.ComponentSchema {
  collectionName: 'components_extra_settings_products_quantities';
  info: {
    description: '';
    displayName: 'Products Quantity';
    icon: 'store';
  };
  attributes: {
    color: Schema.Attribute.String;
    note: Schema.Attribute.Text;
    price: Schema.Attribute.Decimal;
    product: Schema.Attribute.Relation<'oneToOne', 'api::product.product'>;
    quantity: Schema.Attribute.Integer;
    size: Schema.Attribute.String;
    title: Schema.Attribute.String;
    title_ar: Schema.Attribute.String;
  };
}

export interface PermissionsRoles extends Struct.ComponentSchema {
  collectionName: 'components_permissions_roles';
  info: {
    displayName: 'Roles';
    icon: 'cog';
  };
  attributes: {
    name: Schema.Attribute.String;
    name_ar: Schema.Attribute.String;
  };
}

export interface PermissionsSubVendorRoles extends Struct.ComponentSchema {
  collectionName: 'components_permissions_sub_vendor_roles';
  info: {
    displayName: 'Sub Vendor Roles';
    icon: 'collapse';
  };
  attributes: {
    role: Schema.Attribute.String;
    sub_vendor: Schema.Attribute.Relation<'oneToOne', 'api::vendor.vendor'>;
  };
}

export interface QrLandingLanding extends Struct.ComponentSchema {
  collectionName: 'components_qr_landing_landings';
  info: {
    description: '';
    displayName: 'Landing';
    icon: 'code';
  };
  attributes: {
    description: Schema.Attribute.Text;
    link: Schema.Attribute.String;
    title: Schema.Attribute.String;
    type: Schema.Attribute.Enumeration<['social', 'menu']> &
      Schema.Attribute.DefaultTo<'social'>;
  };
}

export interface TemplatesProjects extends Struct.ComponentSchema {
  collectionName: 'components_templates_projects';
  info: {
    displayName: 'Projects';
    icon: 'globe';
  };
  attributes: {
    app_store: Schema.Attribute.String;
    images: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios',
      true
    >;
    name: Schema.Attribute.String;
    play_store: Schema.Attribute.String;
    template: Schema.Attribute.Enumeration<
      ['Clothes', 'Accessories', 'Market', 'Restaurant', 'Default']
    >;
    website: Schema.Attribute.String;
  };
}

export interface TemplatesTemplates extends Struct.ComponentSchema {
  collectionName: 'components_templates_templates';
  info: {
    displayName: 'Templates';
    icon: 'brush';
  };
  attributes: {
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    name: Schema.Attribute.String;
    url: Schema.Attribute.String;
  };
}

export interface VendorWebviewFeatureVendorWebviewFeature
  extends Struct.ComponentSchema {
  collectionName: 'components_vendor_webview_feature_vendor_webview_features';
  info: {
    displayName: 'Vendor Webview Feature';
    icon: 'cursor';
  };
  attributes: {
    icon: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    link: Schema.Attribute.String;
    name: Schema.Attribute.String;
    name_ar: Schema.Attribute.String;
  };
}

declare module '@strapi/strapi' {
  export module Public {
    export interface ComponentSchemas {
      'about.about': AboutAbout;
      'address.address': AddressAddress;
      'areas.areas': AreasAreas;
      'boundaries.boundaries': BoundariesBoundaries;
      'cities-cost.cities-cost': CitiesCostCitiesCost;
      'contact-us.contact-us': ContactUsContactUs;
      'contact-us.contact-us-field': ContactUsContactUsField;
      'extra-settings.extra-settings': ExtraSettingsExtraSettings;
      'extra-settings.product-extra-settings': ExtraSettingsProductExtraSettings;
      'extra-settings.products-quantity': ExtraSettingsProductsQuantity;
      'permissions.roles': PermissionsRoles;
      'permissions.sub-vendor-roles': PermissionsSubVendorRoles;
      'qr-landing.landing': QrLandingLanding;
      'templates.projects': TemplatesProjects;
      'templates.templates': TemplatesTemplates;
      'vendor-webview-feature.vendor-webview-feature': VendorWebviewFeatureVendorWebviewFeature;
    }
  }
}
