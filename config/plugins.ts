const path = require('path');

export default ({ env }) => {
  return {
    upload: {
      config: {
        provider: "strapi-provider-firebase-storage",
        providerOptions: {
          // Corrected absolute path
          serviceAccount: require(path.resolve(process.cwd(), 'idea2app-dev-firebase.json')),
          bucket: env(
            "STORAGE_BUCKET_URL"
          ),
          sortInStorage: true, // true | false
          debug: false, // true | false
        },
      },
    },
   'strapi-v5-plugin-populate-deep': {
     config: {
       defaultDepth: 2, // Default is 5
     },
    },
    'dashboard': {
      enabled: true,
      resolve: './src/plugins/dashboard'
    },

  };
};
