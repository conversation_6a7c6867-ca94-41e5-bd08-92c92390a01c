{"name": "idea-2-app-backend", "private": true, "version": "0.1.0", "description": "A Strapi application", "scripts": {"develop": "strapi develop", "start": "strapi start", "build": "strapi build", "strapi": "strapi", "deploy": "strapi deploy"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5"}, "dependencies": {"@strapi/plugin-users-permissions": "v5.19.0", "@strapi/strapi": "v5.19.0", "pg": "8.8.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "strapi-provider-firebase-storage": "^1.0.4", "strapi-v5-plugin-populate-deep": "^4.0.2", "styled-components": "^6.0.0"}, "author": {"name": "A Strapi developer"}, "strapi": {"uuid": "b93f5be7-4fea-4781-85b7-fe26cfbe791e"}, "engines": {"node": ">=18.17.0 <=20.x.x", "npm": ">=7.0.0"}, "license": "MIT"}