{"version": "0.0.0", "keywords": [], "type": "commonjs", "exports": {"./package.json": "./package.json", "./strapi-admin": {"types": "./dist/admin/src/index.d.ts", "source": "./admin/src/index.ts", "import": "./dist/admin/index.mjs", "require": "./dist/admin/index.js", "default": "./dist/admin/index.js"}, "./strapi-server": {"types": "./dist/server/src/index.d.ts", "source": "./server/src/index.ts", "import": "./dist/server/index.mjs", "require": "./dist/server/index.js", "default": "./dist/server/index.js"}}, "files": ["dist"], "scripts": {"build": "strapi-plugin build", "watch": "strapi-plugin watch", "watch:link": "strapi-plugin watch:link", "verify": "strapi-plugin verify", "test:ts:front": "run -T tsc -p admin/tsconfig.json", "test:ts:back": "run -T tsc -p server/tsconfig.json"}, "dependencies": {"@strapi/design-system": "^2.0.0-rc.29", "@strapi/icons": "^2.0.0-rc.29", "react-intl": "^7.1.11"}, "devDependencies": {"@strapi/strapi": "^5.19.0", "@strapi/sdk-plugin": "^5.3.2", "prettier": "^3.6.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.30.1", "styled-components": "^6.1.19", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@strapi/typescript-utils": "^5.19.0", "typescript": "^5.8.3"}, "peerDependencies": {"@strapi/strapi": "^5.19.0", "@strapi/sdk-plugin": "^5.3.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.30.1", "styled-components": "^6.1.19"}, "strapi": {"kind": "plugin", "name": "dashboard", "displayName": "Dashboard", "description": "Control Everything From Here..."}, "name": "dashboard", "description": "Control Everything From Here...", "license": "MIT", "author": "Idea2App <<EMAIL>>"}