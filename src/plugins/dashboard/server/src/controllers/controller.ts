import type { Core } from "@strapi/strapi";

const controller = ({ strapi }: { strapi: Core.Strapi }) => ({
  async getContentCounts(ctx) {
    try {
      //Get all content types
      const contentTypes = await Object.keys(strapi.contentTypes)
        .filter((uid) => uid.startsWith("api::"))
        .reduce((acc, uid) => {
          const contentType = strapi.contentTypes[uid];
          acc[contentType.info.displayName || uid] = 0;
          return acc;
        }, {});

      // Count entities for each content type using Document Service
      for (const [name, _] of Object.entries(contentTypes)) {
        const uid = Object.keys(strapi.contentTypes).find(
          (key) =>
            strapi.contentTypes[key].info.displayName === name || key === name
        );

        if (uid) {
          // Using the count() method from Document Service instead of strapi.db.query
          const count = await strapi.documents(uid as any).count({});
          contentTypes[name] = count;
        }
      }
      ctx.body = contentTypes;
    } catch (err) {
      ctx.throw(500, err);
    }
  },
});

export default controller;
// import type { Core } from "@strapi/strapi";
//
// const controller = ({ strapi }: { strapi: Core.Strapi }) => ({
//   async getContentCounts(ctx) {
//     try {
//       // TODO: Add custom logic here
//       ctx.body = { message: "Hello from the server" };
//     } catch (err) {
//       ctx.throw(500, err);
//     }
//   },
// });
//
// export default controller;
// // import type { Core } from '@strapi/strapi';
// //
// // const controller = ({ strapi }: { strapi: Core.Strapi }) => ({
// //   index(ctx) {
// //     ctx.body = strapi
// //       .plugin('dashboard')
// //       // the name of the service file & the method.
// //       .service('service')
// //       .getWelcomeMessage();
// //   },
// // });
// //
// // export default controller;
