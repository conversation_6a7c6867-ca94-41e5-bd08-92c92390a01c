import { useNavigate } from 'react-router-dom';
import { useIntl } from 'react-intl';

import { getTranslation } from '../utils/getTranslation';
import { PLUGIN_ID } from '../pluginId';

const NavigationButton = () => {
  const navigate = useNavigate();
  const { formatMessage } = useIntl();

  const handleNavigate = () => {
    navigate(`/plugins/${PLUGIN_ID}/main`);
  };

  return (
    <Box padding={4} background="neutral0" borderRadius="4px" shadow="filterShadow">
      <Flex direction="column" alignItems="center" gap={3}>
        <Stethoscope width="32px" height="32px" />
        <Typography variant="omega" fontWeight="semiBold" textAlign="center">
          {formatMessage({
            id: getTranslation('widget.navigation.title'),
            defaultMessage: 'Dashboard Control Center'
          })}
        </Typography>
        <Typography variant="pi" textColor="neutral600" textAlign="center">
          {formatMessage({
            id: getTranslation('widget.navigation.description'),
            defaultMessage: 'Manage all your content from one place'
          })}
        </Typography>
        <Button
          onClick={handleNavigate}
          variant="primary"
          startIcon={<Stethoscope />}
          size="S"
        >
          {formatMessage({
            id: getTranslation('widget.navigation.button'),
            defaultMessage: 'Go to Dashboard'
          })}
        </Button>
      </Flex>
    </Box>
  );
};

export default NavigationButton;
