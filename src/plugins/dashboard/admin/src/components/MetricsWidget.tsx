import { useNavigate } from 'react-router-dom';
import { useIntl } from 'react-intl';

import { getTranslation } from '../utils/getTranslation';
import { PLUGIN_ID } from '../pluginId';

const NavigationButton = () => {
  const navigate = useNavigate();
  const { formatMessage } = useIntl();

  const handleNavigate = () => {
    navigate(`/plugins/${PLUGIN_ID}/main`);
  };

  return (
    <div style={{
      padding: '16px',
      backgroundColor: '#212134',
      borderRadius: '4px',
      boxShadow: '0 1px 4px rgba(0, 0, 0, 0.2)'
    }}>
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: '12px'
      }}>
        <div style={{ fontSize: '32px' }}>📊</div>
        <h3 style={{
          fontSize: '14px',
          fontWeight: '600',
          textAlign: 'center',
          color: 'white',
          margin: 0
        }}>
          {formatMessage({
            id: getTranslation('widget.navigation.title'),
            defaultMessage: 'Dashboard Control Center'
          })}
        </h3>
        <p style={{
          fontSize: '12px',
          color: '#a5a5ba',
          textAlign: 'center',
          margin: 0
        }}>
          {formatMessage({
            id: getTranslation('widget.navigation.description'),
            defaultMessage: 'Manage all your content from one place'
          })}
        </p>
        <button
          onClick={handleNavigate}
          style={{
            padding: '8px 16px',
            backgroundColor: '#4945ff',
            color: '#ffffff',
            border: 'none',
            borderRadius: '4px',
            fontSize: '14px',
            fontWeight: '500',
            cursor: 'pointer',
            outline: 'none',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}
        >
          <span>📊</span>
          {formatMessage({
            id: getTranslation('widget.navigation.button'),
            defaultMessage: 'Go to Dashboard'
          })}
        </button>
      </div>
    </div>
  );
};

export default NavigationButton;
