import { useState, useRef, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { getTranslation } from '../../utils/getTranslation';

interface VendorOption {
  value: string;
  label: string;
}

interface VendorDropdownProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  label?: string;
}

const VendorDropdown = ({ value, onChange, placeholder, label }: VendorDropdownProps) => {
  const { formatMessage } = useIntl();
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Mock vendor data - this will be replaced with real API data
  const allVendors: VendorOption[] = [
    { value: 'all', label: 'All Vendors' },
    { value: 'vendor1', label: 'Tech Store Pro' },
    { value: 'vendor2', label: 'Fashion Hub' },
    { value: 'vendor3', label: 'Electronics World' },
    { value: 'vendor4', label: 'Home & Garden' },
    { value: 'vendor5', label: 'Sports Center' },
    { value: 'vendor6', label: 'Book Paradise' },
    { value: 'vendor7', label: 'Beauty Corner' },
    { value: 'vendor8', label: 'Auto Parts Plus' },
    { value: 'vendor9', label: 'Mobile Accessories' },
    { value: 'vendor10', label: 'Kitchen Essentials' },
    { value: 'vendor11', label: 'Pet Supplies Store' },
    { value: 'vendor12', label: 'Art & Craft Corner' },
    { value: 'vendor13', label: 'Fitness Equipment' },
    { value: 'vendor14', label: 'Baby Care Center' },
    { value: 'vendor15', label: 'Music Instruments' },
    { value: 'vendor16', label: 'Office Supplies' },
    { value: 'vendor17', label: 'Travel Gear' },
    { value: 'vendor18', label: 'Gaming Zone' },
    { value: 'vendor19', label: 'Health & Wellness' },
    { value: 'vendor20', label: 'Outdoor Adventure' }
  ];

  const filteredVendors = allVendors.filter(vendor =>
    vendor.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const selectedVendor = allVendors.find(vendor => vendor.value === value);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSelect = (selectedValue: string) => {
    onChange(selectedValue);
    setIsOpen(false);
    setSearchTerm('');
  };

  return (
    <div style={{ position: 'relative' }} ref={dropdownRef}>
      {label && (
        <label style={{
          display: 'block',
          fontSize: '12px',
          fontWeight: 'bold',
          marginBottom: '4px',
          color: 'white'
        }}>
          {label}
        </label>
      )}
      
      <div
        onClick={() => setIsOpen(!isOpen)}
        style={{
          width: '100%',
          padding: '8px 32px 8px 12px',
          border: '1px solid #32324d',
          borderRadius: '4px',
          fontSize: '14px',
          outline: 'none',
          backgroundColor: '#181826',
          color: 'white',
          cursor: 'pointer',
          position: 'relative',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}
      >
        <span>{selectedVendor?.label || placeholder || 'Select vendor'}</span>
        <span style={{
          position: 'absolute',
          right: '12px',
          fontSize: '12px',
          color: '#a5a5ba'
        }}>
          {isOpen ? '▲' : '▼'}
        </span>
      </div>

      {isOpen && (
        <div style={{
          position: 'absolute',
          top: '100%',
          left: 0,
          right: 0,
          backgroundColor: '#212134',
          border: '1px solid #32324d',
          borderRadius: '4px',
          boxShadow: '0 4px 8px rgba(0, 0, 0, 0.3)',
          zIndex: 1000,
          maxHeight: '300px',
          overflow: 'hidden'
        }}>
          {/* Search Input */}
          <div style={{ padding: '8px' }}>
            <input
              type="text"
              placeholder="Search vendors..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                width: '100%',
                padding: '6px 8px',
                border: '1px solid #32324d',
                borderRadius: '4px',
                fontSize: '14px',
                outline: 'none',
                backgroundColor: '#181826',
                color: 'white'
              }}
              autoFocus
            />
          </div>

          {/* Options List */}
          <div style={{
            maxHeight: '240px',
            overflowY: 'auto'
          }}>
            {filteredVendors.length > 0 ? (
              filteredVendors.map((vendor) => (
                <div
                  key={vendor.value}
                  onClick={() => handleSelect(vendor.value)}
                  style={{
                    padding: '8px 12px',
                    cursor: 'pointer',
                    backgroundColor: vendor.value === value ? '#4945ff' : 'transparent',
                    color: vendor.value === value ? 'white' : '#a5a5ba',
                    fontSize: '14px'
                  }}
                  onMouseEnter={(e) => {
                    if (vendor.value !== value) {
                      e.currentTarget.style.backgroundColor = '#32324d';
                      e.currentTarget.style.color = 'white';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (vendor.value !== value) {
                      e.currentTarget.style.backgroundColor = 'transparent';
                      e.currentTarget.style.color = '#a5a5ba';
                    }
                  }}
                >
                  {vendor.label}
                </div>
              ))
            ) : (
              <div style={{
                padding: '8px 12px',
                color: '#a5a5ba',
                fontSize: '14px',
                textAlign: 'center'
              }}>
                No vendors found
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default VendorDropdown;
