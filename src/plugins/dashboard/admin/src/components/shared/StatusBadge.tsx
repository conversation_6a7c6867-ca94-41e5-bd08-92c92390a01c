import { Badge } from '@strapi/design-system';

interface StatusBadgeProps {
  status: string;
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'alternative';
}

const StatusBadge = ({ status, variant }: StatusBadgeProps) => {
  const getVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'published':
      case 'active':
      case 'done':
      case 'confirmed':
        return 'success';
      case 'draft':
      case 'pending':
        return 'warning';
      case 'inactive':
      case 'canceled':
      case 'refunded':
        return 'danger';
      case 'delivering':
        return 'primary';
      default:
        return 'secondary';
    }
  };

  return (
    <Badge variant={variant || getVariant(status)}>
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </Badge>
  );
};

export default StatusBadge;
