// Status badge with standard HTML/CSS

interface StatusBadgeProps {
  status: string;
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'alternative';
}

const StatusBadge = ({ status, variant }: StatusBadgeProps) => {
  const getColors = (status: string) => {
    switch (status.toLowerCase()) {
      case 'published':
      case 'active':
      case 'done':
      case 'confirmed':
        return { bg: '#2e7d32', color: '#ffffff' };
      case 'draft':
      case 'pending':
        return { bg: '#ed6c02', color: '#ffffff' };
      case 'inactive':
      case 'canceled':
      case 'refunded':
        return { bg: '#d32f2f', color: '#ffffff' };
      case 'delivering':
        return { bg: '#4945ff', color: '#ffffff' };
      default:
        return { bg: '#666687', color: '#ffffff' };
    }
  };

  const colors = getColors(status);

  return (
    <span style={{
      display: 'inline-block',
      padding: '4px 8px',
      borderRadius: '12px',
      fontSize: '12px',
      fontWeight: '500',
      backgroundColor: colors.bg,
      color: colors.color,
      textTransform: 'capitalize'
    }}>
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
};

export default StatusBadge;
