import { useIntl } from 'react-intl';
import { getTranslation } from '../../utils/getTranslation';

interface FilterBarProps {
  filters: {
    search: string;
    status: string;
    dateRange: any;
  };
  onFiltersChange: (filters: any) => void;
  selectedCollection: string;
}

const FilterBar = ({ filters, onFiltersChange, selectedCollection }: FilterBarProps) => {
  const { formatMessage } = useIntl();

  const handleSearchChange = (value: string) => {
    onFiltersChange({ search: value });
  };

  const handleStatusChange = (value: string) => {
    onFiltersChange({ status: value });
  };

  const handleResetFilters = () => {
    onFiltersChange({
      search: '',
      status: 'all',
      dateRange: null
    });
  };

  const getStatusOptions = () => {
    const baseOptions = [
      { value: 'all', label: 'All Status' },
      { value: 'published', label: 'Published' },
      { value: 'draft', label: 'Draft' }
    ];

    // Add collection-specific status options
    if (selectedCollection === 'orders') {
      return [
        { value: 'all', label: 'All Status' },
        { value: 'pending', label: 'Pending' },
        { value: 'confirmed', label: 'Confirmed' },
        { value: 'delivering', label: 'Delivering' },
        { value: 'done', label: 'Done' },
        { value: 'canceled', label: 'Canceled' },
        { value: 'refunded', label: 'Refunded' }
      ];
    }

    if (selectedCollection === 'vendors' || selectedCollection === 'products') {
      return [
        { value: 'all', label: 'All Status' },
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' },
        { value: 'published', label: 'Published' },
        { value: 'draft', label: 'Draft' }
      ];
    }

    return baseOptions;
  };

  return (
    <div style={{
      backgroundColor: '#ffffff',
      padding: '16px',
      borderRadius: '4px',
      boxShadow: '0 1px 4px rgba(33, 33, 52, 0.1)',
      marginBottom: '24px'
    }}>
      <div style={{
        display: 'flex',
        gap: '16px',
        alignItems: 'end'
      }}>
        {/* Search */}
        <div style={{ flex: 1 }}>
          <label style={{
            display: 'block',
            fontSize: '12px',
            fontWeight: 'bold',
            marginBottom: '4px',
            color: '#32324d'
          }}>
            {formatMessage({
              id: getTranslation('filters.search.label'),
              defaultMessage: 'Search'
            })}
          </label>
          <input
            type="text"
            placeholder={formatMessage({
              id: getTranslation('filters.search.placeholder'),
              defaultMessage: 'Search in all fields...'
            })}
            value={filters.search}
            onChange={(e) => handleSearchChange(e.target.value)}
            style={{
              width: '100%',
              padding: '8px 12px',
              border: '1px solid #dcdce4',
              borderRadius: '4px',
              fontSize: '14px',
              outline: 'none'
            }}
          />
        </div>

        {/* Status Filter */}
        <div style={{ minWidth: '200px' }}>
          <label style={{
            display: 'block',
            fontSize: '12px',
            fontWeight: 'bold',
            marginBottom: '4px',
            color: '#32324d'
          }}>
            {formatMessage({
              id: getTranslation('filters.status.label'),
              defaultMessage: 'Status'
            })}
          </label>
          <select
            value={filters.status}
            onChange={(e) => handleStatusChange(e.target.value)}
            style={{
              width: '100%',
              padding: '8px 12px',
              border: '1px solid #dcdce4',
              borderRadius: '4px',
              fontSize: '14px',
              outline: 'none',
              backgroundColor: '#ffffff'
            }}
          >
            {getStatusOptions().map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Actions */}
        <div style={{ display: 'flex', gap: '8px' }}>
          <button
            onClick={handleResetFilters}
            style={{
              padding: '8px 16px',
              border: '1px solid #dcdce4',
              borderRadius: '4px',
              backgroundColor: '#ffffff',
              color: '#32324d',
              fontSize: '14px',
              cursor: 'pointer',
              outline: 'none'
            }}
          >
            {formatMessage({
              id: getTranslation('filters.reset'),
              defaultMessage: 'Reset'
            })}
          </button>

          <button
            style={{
              padding: '8px 16px',
              border: '1px solid #4945ff',
              borderRadius: '4px',
              backgroundColor: '#4945ff',
              color: '#ffffff',
              fontSize: '14px',
              cursor: 'pointer',
              outline: 'none'
            }}
          >
            {formatMessage({
              id: getTranslation('filters.advanced'),
              defaultMessage: 'Advanced'
            })}
          </button>
        </div>
      </div>
    </div>
  );
};

export default FilterBar;
