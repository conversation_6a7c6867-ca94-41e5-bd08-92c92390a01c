import { useIntl } from 'react-intl';
import { getTranslation } from '../../utils/getTranslation';

interface FilterBarProps {
  filters: {
    search: string;
    vendor: string;
    dateRange: any;
  };
  onFiltersChange: (filters: any) => void;
  selectedCollection: string;
}

const FilterBar = ({ filters, onFiltersChange, selectedCollection }: FilterBarProps) => {
  const { formatMessage } = useIntl();

  const handleSearchChange = (value: string) => {
    onFiltersChange({ search: value });
  };

  const handleVendorChange = (value: string) => {
    onFiltersChange({ vendor: value });
  };

  const handleResetFilters = () => {
    onFiltersChange({
      search: '',
      vendor: 'all',
      dateRange: null
    });
  };

  const getVendorOptions = () => {
    // Mock vendor data - this will be replaced with real data in Phase 2
    return [
      { value: 'all', label: 'All Vendors' },
      { value: 'vendor1', label: 'Tech Store Pro' },
      { value: 'vendor2', label: 'Fashion Hub' },
      { value: 'vendor3', label: 'Electronics World' },
      { value: 'vendor4', label: 'Home & Garden' },
      { value: 'vendor5', label: 'Sports Center' },
      { value: 'vendor6', label: 'Book Paradise' },
      { value: 'vendor7', label: 'Beauty Corner' },
      { value: 'vendor8', label: 'Auto Parts Plus' }
    ];
  };

  return (
    <div style={{
      backgroundColor: '#212134',
      padding: '16px',
      borderRadius: '4px',
      boxShadow: '0 1px 4px rgba(0, 0, 0, 0.2)',
      marginBottom: '24px'
    }}>
      <div style={{
        display: 'flex',
        gap: '16px',
        alignItems: 'end'
      }}>
        {/* Search */}
        <div style={{ flex: 1 }}>
          <label style={{
            display: 'block',
            fontSize: '12px',
            fontWeight: 'bold',
            marginBottom: '4px',
            color: 'white'
          }}>
            {formatMessage({
              id: getTranslation('filters.search.label'),
              defaultMessage: 'Search'
            })}
          </label>
          <input
            type="text"
            placeholder={formatMessage({
              id: getTranslation('filters.search.placeholder'),
              defaultMessage: 'Search in all fields...'
            })}
            value={filters.search}
            onChange={(e) => handleSearchChange(e.target.value)}
            style={{
              width: '100%',
              padding: '8px 12px',
              border: '1px solid #32324d',
              borderRadius: '4px',
              fontSize: '14px',
              outline: 'none',
              backgroundColor: '#181826',
              color: 'white'
            }}
          />
        </div>

        {/* Vendor Filter */}
        <div style={{ minWidth: '200px' }}>
          <label style={{
            display: 'block',
            fontSize: '12px',
            fontWeight: 'bold',
            marginBottom: '4px',
            color: 'white'
          }}>
            {formatMessage({
              id: getTranslation('filters.vendor.label'),
              defaultMessage: 'Vendor'
            })}
          </label>
          <select
            value={filters.vendor}
            onChange={(e) => handleVendorChange(e.target.value)}
            style={{
              width: '100%',
              padding: '8px 12px',
              border: '1px solid #32324d',
              borderRadius: '4px',
              fontSize: '14px',
              outline: 'none',
              backgroundColor: '#181826',
              color: 'white'
            }}
          >
            {getVendorOptions().map(option => (
              <option key={option.value} value={option.value} style={{ backgroundColor: '#181826', color: 'white' }}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Actions */}
        <div style={{ display: 'flex', gap: '8px' }}>
          <button
            onClick={handleResetFilters}
            style={{
              padding: '8px 16px',
              border: '1px solid #32324d',
              borderRadius: '4px',
              backgroundColor: '#181826',
              color: 'white',
              fontSize: '14px',
              cursor: 'pointer',
              outline: 'none'
            }}
          >
            {formatMessage({
              id: getTranslation('filters.reset'),
              defaultMessage: 'Reset'
            })}
          </button>

          <button
            style={{
              padding: '8px 16px',
              border: '1px solid #4945ff',
              borderRadius: '4px',
              backgroundColor: '#4945ff',
              color: '#ffffff',
              fontSize: '14px',
              cursor: 'pointer',
              outline: 'none'
            }}
          >
            {formatMessage({
              id: getTranslation('filters.advanced'),
              defaultMessage: 'Advanced'
            })}
          </button>
        </div>
      </div>
    </div>
  );
};

export default FilterBar;
