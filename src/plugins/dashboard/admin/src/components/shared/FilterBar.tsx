import {
  <PERSON>,
  <PERSON>lex,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Typography
} from '@strapi/design-system';
import { Search, ArrowLeft } from '@strapi/icons';
import { useIntl } from 'react-intl';

import { getTranslation } from '../../utils/getTranslation';

interface FilterBarProps {
  filters: {
    search: string;
    status: string;
    dateRange: any;
  };
  onFiltersChange: (filters: any) => void;
  selectedCollection: string;
}

const FilterBar = ({ filters, onFiltersChange, selectedCollection }: FilterBarProps) => {
  const { formatMessage } = useIntl();

  const handleSearchChange = (value: string) => {
    onFiltersChange({ search: value });
  };

  const handleStatusChange = (value: string) => {
    onFiltersChange({ status: value });
  };

  const handleResetFilters = () => {
    onFiltersChange({
      search: '',
      status: 'all',
      dateRange: null
    });
  };

  const getStatusOptions = () => {
    const baseOptions = [
      { value: 'all', label: 'All Status' },
      { value: 'published', label: 'Published' },
      { value: 'draft', label: 'Draft' }
    ];

    // Add collection-specific status options
    if (selectedCollection === 'orders') {
      return [
        { value: 'all', label: 'All Status' },
        { value: 'pending', label: 'Pending' },
        { value: 'confirmed', label: 'Confirmed' },
        { value: 'delivering', label: 'Delivering' },
        { value: 'done', label: 'Done' },
        { value: 'canceled', label: 'Canceled' },
        { value: 'refunded', label: 'Refunded' }
      ];
    }

    if (selectedCollection === 'vendors' || selectedCollection === 'products') {
      return [
        { value: 'all', label: 'All Status' },
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' },
        { value: 'published', label: 'Published' },
        { value: 'draft', label: 'Draft' }
      ];
    }

    return baseOptions;
  };

  return (
    <Box
      background="neutral0"
      padding={4}
      borderRadius="4px"
      shadow="filterShadow"
    >
      <Flex gap={4} alignItems="end">
        {/* Search */}
        <Box flex="1">
          <Typography variant="pi" fontWeight="bold" marginBottom={1}>
            {formatMessage({
              id: getTranslation('filters.search.label'),
              defaultMessage: 'Search'
            })}
          </Typography>
          <Searchbar
            placeholder={formatMessage({
              id: getTranslation('filters.search.placeholder'),
              defaultMessage: 'Search in all fields...'
            })}
            value={filters.search}
            onChange={handleSearchChange}
            onClear={() => handleSearchChange('')}
          />
        </Box>

        {/* Status Filter */}
        <Box minWidth="200px">
          <Typography variant="pi" fontWeight="bold" marginBottom={1}>
            {formatMessage({
              id: getTranslation('filters.status.label'),
              defaultMessage: 'Status'
            })}
          </Typography>
          <Select
            placeholder={formatMessage({
              id: getTranslation('filters.status.placeholder'),
              defaultMessage: 'Select status'
            })}
            value={filters.status}
            onChange={handleStatusChange}
          >
            {getStatusOptions().map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </Box>

        {/* Actions */}
        <Flex gap={2}>
          <Button
            variant="tertiary"
            startIcon={<ArrowLeft />}
            onClick={handleResetFilters}
            size="L"
          >
            {formatMessage({
              id: getTranslation('filters.reset'),
              defaultMessage: 'Reset'
            })}
          </Button>

          <Button
            variant="secondary"
            startIcon={<Search />}
            size="L"
          >
            {formatMessage({
              id: getTranslation('filters.advanced'),
              defaultMessage: 'Advanced'
            })}
          </Button>
        </Flex>
      </Flex>
    </Box>
  );
};

export default FilterBar;
