import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Flex,
  Button,
  Divider
} from '@strapi/design-system';
import {
  Stethoscope,
  ShoppingCart,
  User,
  Archive,
  Message,
  Check,
  Pin,
  Earth,
  Cog,
  PuzzlePiece,
  ArrowRight,
  Store
} from '@strapi/icons';
import { useIntl } from 'react-intl';

import { getTranslation } from '../../utils/getTranslation';

interface SideMenuProps {
  selectedCollection: string;
  onCollectionChange: (collection: string) => void;
}

const SideMenu = ({ selectedCollection, onCollectionChange }: SideMenuProps) => {
  const { formatMessage } = useIntl();

  const menuItems = [
    {
      id: 'overview',
      label: 'Overview',
      icon: Stethoscope,
      category: 'main'
    },
    {
      id: 'products',
      label: 'Products',
      icon: Archive,
      category: 'commerce',
      count: 156
    },
    {
      id: 'orders',
      label: 'Orders',
      icon: ShoppingCart,
      category: 'commerce',
      count: 89
    },
    {
      id: 'vendors',
      label: 'Vendors',
      icon: Store,
      category: 'commerce',
      count: 23
    },
    {
      id: 'clients',
      label: 'Clients',
      icon: User,
      category: 'users',
      count: 342
    },
    {
      id: 'messages',
      label: 'Messages',
      icon: Message,
      category: 'communication',
      count: 45
    },
    {
      id: 'tasks',
      label: 'Tasks',
      icon: Check,
      category: 'management',
      count: 12
    },
    {
      id: 'cities',
      label: 'Cities',
      icon: Pin,
      category: 'location',
      count: 67
    },
    {
      id: 'countries',
      label: 'Countries',
      icon: Earth,
      category: 'location',
      count: 15
    },
    {
      id: 'currencies',
      label: 'Currencies',
      icon: Archive,
      category: 'settings',
      count: 8
    },
    {
      id: 'shippings',
      label: 'Shippings',
      icon: ArrowRight,
      category: 'commerce',
      count: 5
    },
    {
      id: 'banners',
      label: 'Banners',
      icon: PuzzlePiece,
      category: 'content',
      count: 12
    },
    {
      id: 'configs',
      label: 'Configs',
      icon: Cog,
      category: 'settings',
      count: 3
    }
  ];

  const categories = {
    main: 'Main',
    commerce: 'Commerce',
    users: 'Users',
    communication: 'Communication',
    management: 'Management',
    location: 'Location',
    content: 'Content',
    settings: 'Settings'
  };

  const groupedItems = menuItems.reduce((acc, item) => {
    if (!acc[item.category]) {
      acc[item.category] = [];
    }
    acc[item.category].push(item);
    return acc;
  }, {} as Record<string, typeof menuItems>);

  return (
    <Box
      background="neutral0"
      padding={4}
      borderRadius="4px"
      shadow="filterShadow"
      height="fit-content"
    >
      <Typography variant="delta" marginBottom={4}>
        {formatMessage({
          id: getTranslation('sidemenu.title'),
          defaultMessage: 'Collections'
        })}
      </Typography>

      {Object.entries(groupedItems).map(([category, items], categoryIndex) => (
        <Box key={category} marginBottom={4}>
          {category !== 'main' && (
            <>
              {categoryIndex > 0 && <Divider marginBottom={3} />}
              <Typography
                variant="sigma"
                textColor="neutral600"
                marginBottom={2}
                textTransform="uppercase"
              >
                {categories[category as keyof typeof categories]}
              </Typography>
            </>
          )}

          <Flex direction="column" gap={1}>
            {items.map((item) => {
              const Icon = item.icon;
              const isSelected = selectedCollection === item.id;

              return (
                <Button
                  key={item.id}
                  variant={isSelected ? 'primary' : 'tertiary'}
                  onClick={() => onCollectionChange(item.id)}
                  startIcon={<Icon />}
                  fullWidth
                  justifyContent="flex-start"
                  size="S"
                >
                  <Flex justifyContent="space-between" alignItems="center" width="100%">
                    <Typography
                      variant="omega"
                      textColor={isSelected ? 'primary100' : 'neutral800'}
                    >
                      {item.label}
                    </Typography>
                    {item.count && (
                      <Typography
                        variant="pi"
                        textColor={isSelected ? 'primary200' : 'neutral500'}
                        background={isSelected ? 'primary600' : 'neutral150'}
                        paddingLeft={2}
                        paddingRight={2}
                        borderRadius="12px"
                      >
                        {item.count}
                      </Typography>
                    )}
                  </Flex>
                </Button>
              );
            })}
          </Flex>
        </Box>
      ))}
    </Box>
  );
};

export default SideMenu;
