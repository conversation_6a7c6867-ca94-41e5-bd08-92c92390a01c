import { Box, Typography, Flex, Button } from '@strapi/design-system';
import { ChevronLeft, ChevronRight } from '@strapi/icons';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
}

const Pagination = ({ 
  currentPage, 
  totalPages, 
  totalItems, 
  itemsPerPage, 
  onPageChange 
}: PaginationProps) => {
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  return (
    <Box padding={4} background="neutral0" borderRadius="4px">
      <Flex justifyContent="space-between" alignItems="center">
        <Typography variant="pi" textColor="neutral600">
          Showing {startItem}-{endItem} of {totalItems} items
        </Typography>
        
        <Flex gap={2} alignItems="center">
          <Button
            variant="tertiary"
            startIcon={<ChevronLeft />}
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
            size="S"
          >
            Previous
          </Button>
          
          <Typography variant="pi" textColor="neutral600">
            Page {currentPage} of {totalPages}
          </Typography>
          
          <Button
            variant="tertiary"
            endIcon={<ChevronRight />}
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            size="S"
          >
            Next
          </Button>
        </Flex>
      </Flex>
    </Box>
  );
};

export default Pagination;
