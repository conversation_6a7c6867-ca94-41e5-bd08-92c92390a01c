// Pagination component with dark mode styling

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
}

const Pagination = ({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  onPageChange
}: PaginationProps) => {
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  return (
    <div style={{
      padding: '16px',
      backgroundColor: '#212134',
      borderRadius: '4px',
      boxShadow: '0 1px 4px rgba(0, 0, 0, 0.2)'
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <span style={{
          fontSize: '14px',
          color: '#a5a5ba'
        }}>
          Showing {startItem}-{endItem} of {totalItems} items
        </span>

        <div style={{
          display: 'flex',
          gap: '8px',
          alignItems: 'center'
        }}>
          <button
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
            style={{
              padding: '6px 12px',
              backgroundColor: currentPage === 1 ? '#32324d' : '#181826',
              color: currentPage === 1 ? '#a5a5ba' : 'white',
              border: '1px solid #32324d',
              borderRadius: '4px',
              fontSize: '14px',
              cursor: currentPage === 1 ? 'not-allowed' : 'pointer',
              outline: 'none'
            }}
          >
            ← Previous
          </button>

          <span style={{
            fontSize: '14px',
            color: '#a5a5ba',
            margin: '0 8px'
          }}>
            Page {currentPage} of {totalPages}
          </span>

          <button
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            style={{
              padding: '6px 12px',
              backgroundColor: currentPage === totalPages ? '#32324d' : '#181826',
              color: currentPage === totalPages ? '#a5a5ba' : 'white',
              border: '1px solid #32324d',
              borderRadius: '4px',
              fontSize: '14px',
              cursor: currentPage === totalPages ? 'not-allowed' : 'pointer',
              outline: 'none'
            }}
          >
            Next →
          </button>
        </div>
      </div>
    </div>
  );
};

export default Pagination;
