// DataTable placeholder with standard HTML/CSS

interface DataTableProps {
  collection: string;
  data?: any[];
  filters?: any;
}

const DataTable = ({ collection, data = [], filters }: DataTableProps) => {
  return (
    <div style={{
      backgroundColor: '#212134',
      padding: '24px',
      borderRadius: '4px',
      boxShadow: '0 1px 4px rgba(0, 0, 0, 0.2)'
    }}>
      <h3 style={{
        fontSize: '20px',
        fontWeight: 'bold',
        marginBottom: '16px',
        color: 'white',
        margin: '0 0 16px 0'
      }}>
        DataTable Component
      </h3>
      <p style={{
        fontSize: '14px',
        color: '#a5a5ba',
        marginBottom: '8px'
      }}>
        This will be implemented in Phase 2 with full CRUD functionality, pagination, and filtering.
      </p>
      <p style={{
        fontSize: '12px',
        color: '#666687',
        margin: 0
      }}>
        Collection: {collection} | Items: {data.length}
      </p>
    </div>
  );
};

export default DataTable;
