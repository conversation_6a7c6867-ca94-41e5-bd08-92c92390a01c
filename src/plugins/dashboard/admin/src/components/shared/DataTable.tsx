// DataTable placeholder with standard HTML/CSS

interface DataTableProps {
  collection: string;
  data?: any[];
  filters?: any;
}

const DataTable = ({ collection, data = [], filters }: DataTableProps) => {
  return (
    <Box>
      <Typography variant="beta" marginBottom={4}>
        DataTable Component
      </Typography>
      <Typography variant="omega" textColor="neutral600">
        This will be implemented in Phase 2 with full CRUD functionality, pagination, and filtering.
      </Typography>
      <Typography variant="pi" textColor="neutral500" marginTop={2}>
        Collection: {collection} | Items: {data.length}
      </Typography>
    </Box>
  );
};

export default DataTable;
