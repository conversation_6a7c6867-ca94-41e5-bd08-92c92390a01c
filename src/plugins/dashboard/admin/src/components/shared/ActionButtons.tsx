// Action buttons with standard HTML/CSS

interface ActionButtonsProps {
  onView?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  size?: 'S' | 'M' | 'L';
  variant?: 'icon' | 'button';
}

const ActionButtons = ({
  onView,
  onEdit,
  onDelete,
  size = 'S',
  variant = 'icon'
}: ActionButtonsProps) => {
  const buttonStyle = {
    padding: variant === 'icon' ? '6px' : '8px 12px',
    border: 'none',
    borderRadius: '4px',
    fontSize: '14px',
    cursor: 'pointer',
    outline: 'none',
    display: 'flex',
    alignItems: 'center',
    gap: '4px'
  };

  if (variant === 'icon') {
    return (
      <div style={{ display: 'flex', gap: '4px' }}>
        {onView && (
          <button
            onClick={onView}
            title="View"
            style={{
              ...buttonStyle,
              backgroundColor: '#181826',
              color: '#a5a5ba',
              border: '1px solid #32324d'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#32324d';
              e.currentTarget.style.color = 'white';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#181826';
              e.currentTarget.style.color = '#a5a5ba';
            }}
          >
            👁️
          </button>
        )}
        {onEdit && (
          <button
            onClick={onEdit}
            title="Edit"
            style={{
              ...buttonStyle,
              backgroundColor: '#181826',
              color: '#a5a5ba',
              border: '1px solid #32324d'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#32324d';
              e.currentTarget.style.color = 'white';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#181826';
              e.currentTarget.style.color = '#a5a5ba';
            }}
          >
            ✏️
          </button>
        )}
        {onDelete && (
          <button
            onClick={onDelete}
            title="Delete"
            style={{
              ...buttonStyle,
              backgroundColor: '#d32f2f',
              color: 'white',
              border: '1px solid #d32f2f'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#b71c1c';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#d32f2f';
            }}
          >
            🗑️
          </button>
        )}
      </div>
    );
  }

  return (
    <div style={{ display: 'flex', gap: '8px' }}>
      {onView && (
        <button
          onClick={onView}
          style={{
            ...buttonStyle,
            backgroundColor: '#181826',
            color: 'white',
            border: '1px solid #32324d'
          }}
        >
          👁️ View
        </button>
      )}
      {onEdit && (
        <button
          onClick={onEdit}
          style={{
            ...buttonStyle,
            backgroundColor: '#4945ff',
            color: 'white',
            border: '1px solid #4945ff'
          }}
        >
          ✏️ Edit
        </button>
      )}
      {onDelete && (
        <button
          onClick={onDelete}
          style={{
            ...buttonStyle,
            backgroundColor: '#d32f2f',
            color: 'white',
            border: '1px solid #d32f2f'
          }}
        >
          🗑️ Delete
        </button>
      )}
    </div>
  );
};

export default ActionButtons;
