import { Flex, Button, Icon<PERSON>utton } from '@strapi/design-system';
import { <PERSON>, Pencil, Trash } from '@strapi/icons';

interface ActionButtonsProps {
  onView?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  size?: 'S' | 'M' | 'L';
  variant?: 'icon' | 'button';
}

const ActionButtons = ({ 
  onView, 
  onEdit, 
  onDelete, 
  size = 'S',
  variant = 'icon'
}: ActionButtonsProps) => {
  if (variant === 'icon') {
    return (
      <Flex gap={1}>
        {onView && (
          <IconButton
            onClick={onView}
            label="View"
            icon={<Eye />}
            variant="tertiary"
          />
        )}
        {onEdit && (
          <IconButton
            onClick={onEdit}
            label="Edit"
            icon={<Pencil />}
            variant="tertiary"
          />
        )}
        {onDelete && (
          <IconButton
            onClick={onDelete}
            label="Delete"
            icon={<Trash />}
            variant="danger-light"
          />
        )}
      </Flex>
    );
  }

  return (
    <Flex gap={2}>
      {onView && (
        <Button
          onClick={onView}
          variant="tertiary"
          startIcon={<Eye />}
          size={size}
        >
          View
        </Button>
      )}
      {onEdit && (
        <Button
          onClick={onEdit}
          variant="secondary"
          startIcon={<Pencil />}
          size={size}
        >
          Edit
        </Button>
      )}
      {onDelete && (
        <Button
          onClick={onDelete}
          variant="danger-light"
          startIcon={<Trash />}
          size={size}
        >
          Delete
        </Button>
      )}
    </Flex>
  );
};

export default ActionButtons;
