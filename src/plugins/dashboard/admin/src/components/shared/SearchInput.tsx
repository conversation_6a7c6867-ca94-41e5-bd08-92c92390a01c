import { Searchbar } from '@strapi/design-system';

interface SearchInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

const SearchInput = ({ value, onChange, placeholder = 'Search...' }: SearchInputProps) => {
  return (
    <Searchbar
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      onClear={() => onChange('')}
    />
  );
};

export default SearchInput;
