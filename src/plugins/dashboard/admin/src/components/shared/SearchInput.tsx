// Search input with standard HTML/CSS

interface SearchInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

const SearchInput = ({ value, onChange, placeholder = 'Search...' }: SearchInputProps) => {
  return (
    <div style={{ position: 'relative' }}>
      <input
        type="text"
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        style={{
          width: '100%',
          padding: '8px 32px 8px 12px',
          border: '1px solid #32324d',
          borderRadius: '4px',
          fontSize: '14px',
          outline: 'none',
          backgroundColor: '#181826',
          color: 'white'
        }}
      />
      {value && (
        <button
          onClick={() => onChange('')}
          style={{
            position: 'absolute',
            right: '8px',
            top: '50%',
            transform: 'translateY(-50%)',
            background: 'none',
            border: 'none',
            color: '#a5a5ba',
            cursor: 'pointer',
            fontSize: '16px'
          }}
        >
          ✕
        </button>
      )}
    </div>
  );
};

export default SearchInput;
