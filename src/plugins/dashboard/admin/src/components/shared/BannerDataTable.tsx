import { useState } from 'react';
import { useIntl } from 'react-intl';
import { getTranslation } from '../../utils/getTranslation';
import VendorDropdown from './VendorDropdown';
import StatusBadge from './StatusBadge';
import ActionButtons from './ActionButtons';
import Pagination from './Pagination';

interface Banner {
  id: string;
  title: string;
  description: string;
  image: string;
  vendor: {
    id: string;
    name: string;
  };
  status: 'published' | 'draft';
  createdAt: string;
  updatedAt: string;
}

interface BannerDataTableProps {
  searchTerm: string;
  vendorFilter: string;
}

const BannerDataTable = ({ searchTerm, vendorFilter }: BannerDataTableProps) => {
  const { formatMessage } = useIntl();
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  // Mock banner data - this will be replaced with real API data
  const mockBanners: Banner[] = [
    {
      id: '1',
      title: 'Summer Sale Banner',
      description: 'Promotional banner for summer collection',
      image: '/uploads/banner1.jpg',
      vendor: { id: 'vendor1', name: 'Tech Store Pro' },
      status: 'published',
      createdAt: '2024-01-15',
      updatedAt: '2024-01-20'
    },
    {
      id: '2',
      title: 'New Arrivals',
      description: 'Showcase latest products',
      image: '/uploads/banner2.jpg',
      vendor: { id: 'vendor2', name: 'Fashion Hub' },
      status: 'draft',
      createdAt: '2024-01-10',
      updatedAt: '2024-01-18'
    },
    {
      id: '3',
      title: 'Black Friday Special',
      description: 'Limited time offer banner',
      image: '/uploads/banner3.jpg',
      vendor: { id: 'vendor3', name: 'Electronics World' },
      status: 'published',
      createdAt: '2024-01-05',
      updatedAt: '2024-01-15'
    },
    {
      id: '4',
      title: 'Holiday Collection',
      description: 'Festive season banner',
      image: '/uploads/banner4.jpg',
      vendor: { id: 'vendor1', name: 'Tech Store Pro' },
      status: 'draft',
      createdAt: '2024-01-12',
      updatedAt: '2024-01-22'
    },
    {
      id: '5',
      title: 'Spring Launch',
      description: 'New spring collection announcement',
      image: '/uploads/banner5.jpg',
      vendor: { id: 'vendor4', name: 'Home & Garden' },
      status: 'published',
      createdAt: '2024-01-08',
      updatedAt: '2024-01-16'
    }
  ];

  // Filter banners based on search and vendor
  const filteredBanners = mockBanners.filter(banner => {
    const matchesSearch = banner.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         banner.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesVendor = vendorFilter === 'all' || banner.vendor.id === vendorFilter;
    return matchesSearch && matchesVendor;
  });

  // Pagination
  const totalItems = filteredBanners.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentBanners = filteredBanners.slice(startIndex, endIndex);

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(currentBanners.map(banner => banner.id));
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelectItem = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedItems([...selectedItems, id]);
    } else {
      setSelectedItems(selectedItems.filter(item => item !== id));
    }
  };

  const handleView = (id: string) => {
    console.log('View banner:', id);
    // TODO: Implement view modal
  };

  const handleEdit = (id: string) => {
    console.log('Edit banner:', id);
    // TODO: Implement edit modal
  };

  const handleDelete = (id: string) => {
    console.log('Delete banner:', id);
    // TODO: Implement delete confirmation
  };

  const handleBulkDelete = () => {
    console.log('Bulk delete:', selectedItems);
    // TODO: Implement bulk delete
  };

  return (
    <div>
      {/* Table Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '16px'
      }}>
        <h3 style={{
          fontSize: '18px',
          fontWeight: 'bold',
          color: 'white',
          margin: 0
        }}>
          Banners ({totalItems})
        </h3>
        
        <div style={{ display: 'flex', gap: '8px' }}>
          {selectedItems.length > 0 && (
            <button
              onClick={handleBulkDelete}
              style={{
                padding: '8px 16px',
                backgroundColor: '#d32f2f',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                fontSize: '14px',
                cursor: 'pointer'
              }}
            >
              Delete Selected ({selectedItems.length})
            </button>
          )}
          
          <button
            style={{
              padding: '8px 16px',
              backgroundColor: '#4945ff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              fontSize: '14px',
              cursor: 'pointer'
            }}
          >
            + Add Banner
          </button>
        </div>
      </div>

      {/* Table */}
      <div style={{
        backgroundColor: '#181826',
        borderRadius: '4px',
        border: '1px solid #32324d',
        overflow: 'hidden'
      }}>
        {/* Table Header */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: '40px 80px 1fr 1fr 150px 100px 120px 100px',
          gap: '16px',
          padding: '12px 16px',
          backgroundColor: '#32324d',
          borderBottom: '1px solid #32324d',
          fontSize: '12px',
          fontWeight: 'bold',
          color: '#a5a5ba',
          textTransform: 'uppercase'
        }}>
          <input
            type="checkbox"
            checked={selectedItems.length === currentBanners.length && currentBanners.length > 0}
            onChange={(e) => handleSelectAll(e.target.checked)}
            style={{ margin: 0 }}
          />
          <span>Image</span>
          <span>Title</span>
          <span>Description</span>
          <span>Vendor</span>
          <span>Status</span>
          <span>Updated</span>
          <span>Actions</span>
        </div>

        {/* Table Body */}
        {currentBanners.length > 0 ? (
          currentBanners.map((banner) => (
            <div
              key={banner.id}
              style={{
                display: 'grid',
                gridTemplateColumns: '40px 80px 1fr 1fr 150px 100px 120px 100px',
                gap: '16px',
                padding: '12px 16px',
                borderBottom: '1px solid #32324d',
                alignItems: 'center',
                fontSize: '14px',
                color: 'white'
              }}
            >
              <input
                type="checkbox"
                checked={selectedItems.includes(banner.id)}
                onChange={(e) => handleSelectItem(banner.id, e.target.checked)}
                style={{ margin: 0 }}
              />
              
              <div style={{
                width: '60px',
                height: '40px',
                backgroundColor: '#32324d',
                borderRadius: '4px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '20px'
              }}>
                🖼️
              </div>
              
              <span style={{ fontWeight: '500' }}>{banner.title}</span>
              <span style={{ color: '#a5a5ba' }}>{banner.description}</span>
              <span style={{ color: '#a5a5ba' }}>{banner.vendor.name}</span>
              
              <StatusBadge status={banner.status} />
              
              <span style={{ color: '#a5a5ba', fontSize: '12px' }}>
                {new Date(banner.updatedAt).toLocaleDateString()}
              </span>
              
              <ActionButtons
                onView={() => handleView(banner.id)}
                onEdit={() => handleEdit(banner.id)}
                onDelete={() => handleDelete(banner.id)}
                variant="icon"
              />
            </div>
          ))
        ) : (
          <div style={{
            padding: '40px',
            textAlign: 'center',
            color: '#a5a5ba'
          }}>
            No banners found
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div style={{ marginTop: '16px' }}>
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalItems}
            itemsPerPage={itemsPerPage}
            onPageChange={setCurrentPage}
          />
        </div>
      )}
    </div>
  );
};

export default BannerDataTable;
