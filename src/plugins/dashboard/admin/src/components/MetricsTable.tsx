import { useState, useEffect } from 'react';
import { useFetchClient } from '@strapi/strapi/admin';
import { Table, Tbody, Tr, Td, Typography, Loader, Alert } from "@strapi/design-system";

import { PLUGIN_ID } from '../pluginId';

const PATH = '/count';

interface MetricsTableProps {
  showTitle?: boolean;
}

const MetricsTable = ({ showTitle = false }: MetricsTableProps) => {
  const { get } = useFetchClient();

  const [loading, setLoading] = useState(true);
  const [metrics, setMetrics] = useState<Record<string, string | number> | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        const { data } = await get(PLUGIN_ID + PATH);
        console.log('data:', data);

        const formattedData: Record<string, string | number> = {};

        if (data && typeof data === 'object') {
          await Promise.all(
            Object.entries(data).map(async ([key, value]) => {
              if (typeof value === 'function') {
                const result = await value();
                formattedData[key] = typeof result === 'number' ? result : String(result);
              } else {
                formattedData[key] = typeof value === 'number' ? value : String(value);
              }
            })
          );
        }

        setMetrics(formattedData);
        setLoading(false);
      } catch (err) {
        console.error(err);
        setError(err instanceof Error ? err.message : 'An error occurred');
        setLoading(false);
      }
    };

    fetchMetrics();
  }, []);

  if (loading) {
    return <Loader>Loading metrics...</Loader>;
  }

  if (error) {
    return <Alert closeLabel="Close" title="Error" variant="danger">{error}</Alert>;
  }

  if (!metrics || Object.keys(metrics).length === 0) {
    return <Alert closeLabel="Close" title="No Data" variant="default">No content types found</Alert>;
  }

  return (
    <div>
      {showTitle && (
        <Typography variant="alpha" marginBottom={4}>
          Content Metrics
        </Typography>
      )}
      <Table>
        <Tbody>
          {Object.entries(metrics).map(([contentType, count], index) => (
            <Tr key={index}>
              <Td>
                <Typography variant="omega">{String(contentType)}</Typography>
              </Td>
              <Td>
                <Typography variant="omega" fontWeight="bold">
                  {String(count)}
                </Typography>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </div>
  );
};

export default MetricsTable;
