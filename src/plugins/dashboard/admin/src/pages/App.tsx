import { Page } from '@strapi/strapi/admin';
import { Routes, Route } from 'react-router-dom';

import { HomePage } from './HomePage';
import { DashboardPage } from './DashboardPage';

const App = () => {
  return (
    <Routes>
      <Route index element={<HomePage />} />
      <Route path="main" element={<DashboardPage />} />
      <Route path="*" element={<Page.Error />} />
    </Routes>
  );
};

export { App };
