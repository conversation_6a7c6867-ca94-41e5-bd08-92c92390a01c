import { Main, Box, Typography } from '@strapi/design-system';
import { useIntl } from 'react-intl';

import { getTranslation } from '../utils/getTranslation';
import MetricsTable from '../components/MetricsTable';

const HomePage = () => {
  const { formatMessage } = useIntl();

  return (
    <Main>
      <Box padding={8}>
        <Typography variant="alpha" marginBottom={6}>
          Welcome to {formatMessage({ id: getTranslation('plugin.name') })}
        </Typography>

        <MetricsTable showTitle={true} />
      </Box>
    </Main>
  );
};

export { HomePage };
