import { useIntl } from 'react-intl';
import { useNavigate } from 'react-router-dom';

import { getTranslation } from '../utils/getTranslation';
import { PLUGIN_ID } from '../pluginId';

const HomePage = () => {
  const { formatMessage } = useIntl();
  const navigate = useNavigate();

  const handleNavigateToDashboard = () => {
    navigate(`/plugins/${PLUGIN_ID}/main`);
  };

  return (
    <main style={{
      padding: '32px',
      backgroundColor: '#181826',
      minHeight: '100vh'
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <h1 style={{
          fontSize: '32px',
          fontWeight: 'bold',
          color: 'white',
          margin: 0
        }}>
          Welcome to {formatMessage({ id: getTranslation('plugin.name') })}
        </h1>

        <button
          onClick={handleNavigateToDashboard}
          style={{
            padding: '12px 24px',
            backgroundColor: '#4945ff',
            color: '#ffffff',
            border: 'none',
            borderRadius: '4px',
            fontSize: '14px',
            fontWeight: '500',
            cursor: 'pointer',
            outline: 'none',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}
        >
          <span>📊</span>
          {formatMessage({
            id: getTranslation('homepage.dashboard.button'),
            defaultMessage: 'Go to Dashboard'
          })}
        </button>
      </div>

      <p style={{
        fontSize: '14px',
        color: '#a5a5ba',
        marginBottom: '24px'
      }}>
        {formatMessage({
          id: getTranslation('homepage.description'),
          defaultMessage: 'Quick overview of your content. Use the dashboard for detailed management.'
        })}
      </p>


    </main>
  );
};

export { HomePage };
