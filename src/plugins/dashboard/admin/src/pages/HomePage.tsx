import { Main, Box, Typography, Button, Flex } from '@strapi/design-system';
import { Dashboard } from '@strapi/icons';
import { useIntl } from 'react-intl';
import { useNavigate } from 'react-router-dom';

import { getTranslation } from '../utils/getTranslation';
import { PLUGIN_ID } from '../pluginId';
import MetricsTable from '../components/MetricsTable';

const HomePage = () => {
  const { formatMessage } = useIntl();
  const navigate = useNavigate();

  const handleNavigateToDashboard = () => {
    navigate(`/plugins/${PLUGIN_ID}/main`);
  };

  return (
    <Main>
      <Box padding={8}>
        <Flex justifyContent="space-between" alignItems="center" marginBottom={6}>
          <Typography variant="alpha">
            Welcome to {formatMessage({ id: getTranslation('plugin.name') })}
          </Typography>

          <Button
            onClick={handleNavigateToDashboard}
            variant="primary"
            startIcon={<Dashboard />}
          >
            {formatMessage({
              id: getTranslation('homepage.dashboard.button'),
              defaultMessage: 'Go to Dashboard'
            })}
          </Button>
        </Flex>

        <Typography variant="omega" textColor="neutral600" marginBottom={6}>
          {formatMessage({
            id: getTranslation('homepage.description'),
            defaultMessage: 'Quick overview of your content. Use the dashboard for detailed management.'
          })}
        </Typography>

        <MetricsTable showTitle={true} />
      </Box>
    </Main>
  );
};

export { HomePage };
