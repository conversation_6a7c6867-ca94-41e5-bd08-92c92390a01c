import { useState } from 'react';
import { 
  Main, 
  Box, 
  Typography, 
  Grid, 
  GridItem,
  Flex,
  Divider
} from '@strapi/design-system';
import { useIntl } from 'react-intl';

import { getTranslation } from '../utils/getTranslation';
import FilterBar from '../components/shared/FilterBar';
import SideMenu from '../components/shared/SideMenu';

const DashboardPage = () => {
  const { formatMessage } = useIntl();
  const [selectedCollection, setSelectedCollection] = useState('overview');
  const [filters, setFilters] = useState({
    search: '',
    status: 'all',
    dateRange: null
  });

  const handleCollectionChange = (collection: string) => {
    setSelectedCollection(collection);
  };

  const handleFiltersChange = (newFilters: any) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  return (
    <Main>
      <Box padding={8}>
        {/* Header */}
        <Flex justifyContent="space-between" alignItems="center" marginBottom={6}>
          <Typography variant="alpha">
            {formatMessage({
              id: getTranslation('dashboard.title'),
              defaultMessage: 'Dashboard Control Center'
            })}
          </Typography>
        </Flex>

        {/* Filter Bar */}
        <Box marginBottom={6}>
          <FilterBar 
            filters={filters}
            onFiltersChange={handleFiltersChange}
            selectedCollection={selectedCollection}
          />
        </Box>

        <Divider marginBottom={6} />

        {/* Main Content Grid */}
        <Grid gap={6}>
          {/* Side Menu */}
          <GridItem col={3}>
            <SideMenu 
              selectedCollection={selectedCollection}
              onCollectionChange={handleCollectionChange}
            />
          </GridItem>

          {/* Content Area */}
          <GridItem col={9}>
            <Box 
              background="neutral0" 
              padding={6} 
              borderRadius="4px" 
              shadow="filterShadow"
              minHeight="600px"
            >
              {selectedCollection === 'overview' ? (
                <Box>
                  <Typography variant="beta" marginBottom={4}>
                    {formatMessage({
                      id: getTranslation('dashboard.overview.title'),
                      defaultMessage: 'Overview'
                    })}
                  </Typography>
                  <Typography variant="omega" textColor="neutral600">
                    {formatMessage({
                      id: getTranslation('dashboard.overview.description'),
                      defaultMessage: 'Select a collection from the sidebar to manage your content.'
                    })}
                  </Typography>
                </Box>
              ) : (
                <Box>
                  <Typography variant="beta" marginBottom={4}>
                    {selectedCollection.charAt(0).toUpperCase() + selectedCollection.slice(1)}
                  </Typography>
                  <Typography variant="omega" textColor="neutral600">
                    {formatMessage({
                      id: getTranslation('dashboard.collection.placeholder'),
                      defaultMessage: `Managing ${selectedCollection} collection. Data table will be implemented in Phase 2.`
                    })}
                  </Typography>
                </Box>
              )}
            </Box>
          </GridItem>
        </Grid>
      </Box>
    </Main>
  );
};

export { DashboardPage };
