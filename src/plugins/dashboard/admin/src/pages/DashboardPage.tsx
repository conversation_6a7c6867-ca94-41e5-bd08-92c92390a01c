import { useState } from 'react';
import { useIntl } from 'react-intl';

import { getTranslation } from '../utils/getTranslation';
import FilterBar from '../components/shared/FilterBar';
import SideMenu from '../components/shared/SideMenu';

const DashboardPage = () => {
  const { formatMessage } = useIntl();
  const [selectedCollection, setSelectedCollection] = useState('overview');
  const [filters, setFilters] = useState({
    search: '',
    status: 'all',
    dateRange: null
  });

  const handleCollectionChange = (collection: string) => {
    setSelectedCollection(collection);
  };

  const handleFiltersChange = (newFilters: any) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  return (
    <main style={{ padding: '32px' }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <h1 style={{
          fontSize: '32px',
          fontWeight: 'bold',
          color: '#32324d',
          margin: 0
        }}>
          {formatMessage({
            id: getTranslation('dashboard.title'),
            defaultMessage: 'Dashboard Control Center'
          })}
        </h1>
      </div>

      {/* Filter Bar */}
      <FilterBar
        filters={filters}
        onFiltersChange={handleFiltersChange}
        selectedCollection={selectedCollection}
      />

      <hr style={{
        border: 'none',
        borderTop: '1px solid #eaeaef',
        margin: '24px 0'
      }} />

      {/* Main Content Layout */}
      <div style={{
        display: 'flex',
        gap: '24px'
      }}>
        {/* Side Menu */}
        <div style={{
          width: '300px',
          flexShrink: 0
        }}>
          <SideMenu
            selectedCollection={selectedCollection}
            onCollectionChange={handleCollectionChange}
          />
        </div>

        {/* Content Area */}
        <div style={{ flex: 1 }}>
          <div style={{
            backgroundColor: '#ffffff',
            padding: '24px',
            borderRadius: '4px',
            boxShadow: '0 1px 4px rgba(33, 33, 52, 0.1)',
            minHeight: '600px'
          }}>
            {selectedCollection === 'overview' ? (
              <div>
                <h2 style={{
                  fontSize: '24px',
                  fontWeight: 'bold',
                  marginBottom: '16px',
                  color: '#32324d',
                  margin: '0 0 16px 0'
                }}>
                  {formatMessage({
                    id: getTranslation('dashboard.overview.title'),
                    defaultMessage: 'Overview'
                  })}
                </h2>
                <p style={{
                  fontSize: '14px',
                  color: '#666687',
                  margin: 0
                }}>
                  {formatMessage({
                    id: getTranslation('dashboard.overview.description'),
                    defaultMessage: 'Select a collection from the sidebar to manage your content.'
                  })}
                </p>
              </div>
            ) : (
              <div>
                <h2 style={{
                  fontSize: '24px',
                  fontWeight: 'bold',
                  marginBottom: '16px',
                  color: '#32324d',
                  margin: '0 0 16px 0'
                }}>
                  {selectedCollection.charAt(0).toUpperCase() + selectedCollection.slice(1)}
                </h2>
                <p style={{
                  fontSize: '14px',
                  color: '#666687',
                  margin: 0
                }}>
                  {formatMessage({
                    id: getTranslation('dashboard.collection.placeholder'),
                    defaultMessage: `Managing ${selectedCollection} collection. Data table will be implemented in Phase 2.`
                  })}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </main>
  );
};

export { DashboardPage };
