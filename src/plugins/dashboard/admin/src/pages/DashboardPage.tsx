import { useState } from 'react';
import { useIntl } from 'react-intl';

import { getTranslation } from '../utils/getTranslation';
import FilterBar from '../components/shared/FilterBar';
import SideMenu from '../components/shared/SideMenu';
import BannerDataTable from '../components/shared/BannerDataTable';

const DashboardPage = () => {
  const { formatMessage } = useIntl();
  const [selectedCollection, setSelectedCollection] = useState('overview');
  const [filters, setFilters] = useState({
    search: '',
    vendor: 'all',
    dateRange: null
  });

  const handleCollectionChange = (collection: string) => {
    setSelectedCollection(collection);
  };

  const handleFiltersChange = (newFilters: any) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  return (
    <main style={{
      padding: '32px',
      backgroundColor: '#181826',
      minHeight: '100vh'
    }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <h1 style={{
          fontSize: '32px',
          fontWeight: 'bold',
          color: 'white',
          margin: 0
        }}>
          {formatMessage({
            id: getTranslation('dashboard.title'),
            defaultMessage: 'Dashboard Control Center'
          })}
        </h1>
      </div>

      {/* Filter Bar */}
      <FilterBar
        filters={filters}
        onFiltersChange={handleFiltersChange}
        selectedCollection={selectedCollection}
      />

      <hr style={{
        border: 'none',
        borderTop: '1px solid #32324d',
        margin: '24px 0'
      }} />

      {/* Main Content Layout */}
      <div style={{
        display: 'flex',
        gap: '24px'
      }}>
        {/* Side Menu */}
        <div style={{
          width: '300px',
          flexShrink: 0
        }}>
          <SideMenu
            selectedCollection={selectedCollection}
            onCollectionChange={handleCollectionChange}
          />
        </div>

        {/* Content Area */}
        <div style={{ flex: 1 }}>
          <div style={{
            backgroundColor: '#212134',
            padding: '24px',
            borderRadius: '4px',
            boxShadow: '0 1px 4px rgba(0, 0, 0, 0.2)',
            minHeight: '600px'
          }}>
            {selectedCollection === 'overview' ? (
              <div>
                <h2 style={{
                  fontSize: '24px',
                  fontWeight: 'bold',
                  marginBottom: '16px',
                  color: 'white',
                  margin: '0 0 16px 0'
                }}>
                  {formatMessage({
                    id: getTranslation('dashboard.overview.title'),
                    defaultMessage: 'Overview'
                  })}
                </h2>
                <p style={{
                  fontSize: '14px',
                  color: '#a5a5ba',
                  margin: 0
                }}>
                  {formatMessage({
                    id: getTranslation('dashboard.overview.description'),
                    defaultMessage: 'Select a collection from the sidebar to manage your content.'
                  })}
                </p>
              </div>
            ) : selectedCollection === 'banners' ? (
              <div>
                <h2 style={{
                  fontSize: '24px',
                  fontWeight: 'bold',
                  marginBottom: '16px',
                  color: 'white',
                  margin: '0 0 16px 0'
                }}>
                  Banners Collection
                </h2>
                <p style={{
                  fontSize: '14px',
                  color: '#a5a5ba',
                  marginBottom: '24px'
                }}>
                  Manage your banner images with full CRUD operations, media picker, and vendor filtering.
                </p>

                <BannerDataTable
                  searchTerm={filters.search}
                  vendorFilter={filters.vendor}
                />
              </div>
            ) : (
              <div>
                <h2 style={{
                  fontSize: '24px',
                  fontWeight: 'bold',
                  marginBottom: '16px',
                  color: 'white',
                  margin: '0 0 16px 0'
                }}>
                  {selectedCollection.charAt(0).toUpperCase() + selectedCollection.slice(1)} Collection
                </h2>
                <p style={{
                  fontSize: '14px',
                  color: '#a5a5ba',
                  marginBottom: '24px'
                }}>
                  Manage your {selectedCollection} data with full CRUD operations, search, and filtering.
                </p>

                {/* Data Table will be implemented for other collections */}
                <div style={{
                  backgroundColor: '#181826',
                  padding: '20px',
                  borderRadius: '4px',
                  border: '1px solid #32324d'
                }}>
                  <p style={{
                    color: '#a5a5ba',
                    textAlign: 'center',
                    margin: 0
                  }}>
                    📊 Data table for {selectedCollection} will be implemented next
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </main>
  );
};

export { DashboardPage };
