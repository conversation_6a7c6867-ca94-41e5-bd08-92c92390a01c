{"collectionName": "components_cities_cost_cities_costs", "info": {"displayName": "Cities Cost", "description": ""}, "options": {}, "attributes": {"city": {"type": "relation", "relation": "oneToOne", "target": "api::city.city"}, "cost": {"type": "decimal"}, "free_shipping": {"type": "boolean", "default": false}, "areas": {"displayName": "Areas", "type": "component", "repeatable": true, "component": "areas.areas"}, "is_active": {"type": "boolean", "default": true}}}