{"collectionName": "components_extra_settings_products_quantities", "info": {"displayName": "Products Quantity", "icon": "store", "description": ""}, "options": {}, "attributes": {"title": {"type": "string"}, "title_ar": {"type": "string"}, "price": {"type": "decimal"}, "quantity": {"type": "integer"}, "product": {"type": "relation", "relation": "oneToOne", "target": "api::product.product"}, "size": {"type": "string"}, "color": {"type": "string"}, "note": {"type": "text"}}}