{"collectionName": "components_contact_us_contact_uses", "info": {"displayName": "Contact Us", "icon": "phone", "description": ""}, "options": {}, "attributes": {"email": {"displayName": "Contact Us Field", "type": "component", "repeatable": false, "component": "contact-us.contact-us-field"}, "facebook": {"type": "component", "repeatable": false, "component": "contact-us.contact-us-field"}, "tiktok": {"type": "component", "repeatable": false, "component": "contact-us.contact-us-field"}, "instagram": {"type": "component", "repeatable": false, "component": "contact-us.contact-us-field"}, "whatsapp": {"type": "component", "repeatable": false, "component": "contact-us.contact-us-field"}, "youtube": {"type": "component", "repeatable": false, "component": "contact-us.contact-us-field"}, "website": {"type": "component", "repeatable": false, "component": "contact-us.contact-us-field"}}}