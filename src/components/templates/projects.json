{"collectionName": "components_templates_projects", "info": {"displayName": "Projects", "icon": "globe"}, "options": {}, "attributes": {"name": {"type": "string"}, "template": {"type": "enumeration", "enum": ["<PERSON><PERSON><PERSON>", "Accessories", "Market", "Restaurant", "<PERSON><PERSON><PERSON>"]}, "website": {"type": "string"}, "play_store": {"type": "string"}, "app_store": {"type": "string"}, "images": {"allowedTypes": ["images", "files", "videos", "audios"], "type": "media", "multiple": true}}}