{"kind": "collectionType", "collectionName": "subscription_requests", "info": {"singularName": "subscription-request", "pluralName": "subscription-requests", "displayName": "Subscription Requests", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"vendor": {"type": "relation", "relation": "oneToOne", "target": "api::vendor.vendor"}, "pricing_plan": {"type": "relation", "relation": "oneToOne", "target": "api::price.price"}, "payment_attachment": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "is_approved": {"type": "boolean"}, "payment_method": {"type": "enumeration", "enum": ["instapay", "vodafone_cash"]}, "paid_amount": {"type": "decimal"}}}