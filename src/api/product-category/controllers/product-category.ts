
import { factories } from '@strapi/strapi';
import { formatMedia } from '../../../utils/formatMedia';

export default factories.createCoreController('api::product-category.product-category', ({ strapi }) => {
  const formatCategory = (category: any) => ({
    ...category,
    feature_image: formatMedia(category.feature_image),
  });

  return {
    async find(ctx) {
      try {
        const sanitizedQueryParams = await this.sanitizeQuery(ctx);
        const { results, pagination } = await strapi.service('api::product-category.product-category').find(sanitizedQueryParams);

        const formattedResults = results.map(formatCategory);

        const sanitizedResults = await this.sanitizeOutput(formattedResults, ctx);
        return this.transformResponse(sanitizedResults, { pagination });
      } catch (err) {
        console.error('Error in find method:', err);
        return ctx.badRequest('Error fetching product categories');
      }
    },

    async findOne(ctx) {
      try {
        const { id } = ctx.params;
        const sanitizedQueryParams = await this.sanitizeQuery(ctx);
        const result = await strapi.service('api::product-category.product-category').findOne(id, sanitizedQueryParams);

        const sanitizedResult = await this.sanitizeOutput(formatCategory(result), ctx);
        return this.transformResponse(sanitizedResult);
      } catch (err) {
        console.error('Error in findOne method:', err);
        return ctx.badRequest('Error fetching product category');
      }
    },

async bulkUpdateCategories(ctx) {
  try {
    const { data } = ctx.request.body;
    if (!data || typeof data !== 'object') {
      return ctx.badRequest('Invalid request payload.');
    }

    const results = [];

    for (const documentId in data) {
      const payload = data[documentId];
      const existing = await strapi.db.query('api::product-category.product-category').findOne({
        where: { documentId },
      });

      if (!existing) {
        results.push({ documentId, success: false, error: 'Not found' });
        continue;
      }

      try {
        const updated = await strapi.entityService.update(
          'api::product-category.product-category',
          existing.id,
          {
            data: payload,
          }
        );

        results.push({
          documentId,
          success: true,
          data: updated,
        });
      } catch (err) {
        results.push({
          documentId,
          success: false,
          error: err.message,
        });
      }
    }

    const successful = results.filter(r => r.success).length;
    const failed = results.length - successful;

    ctx.send({
      summary: { total: results.length, successful, failed },
      results,
    });
  } catch (err) {
    console.error(err);
    ctx.badRequest({ message: 'bulkUpdateCategories failed', error: err.message });
  }
}



//   async bulkUpdate(ctx) {
//     try {
//       const { main_categories = {}, categories = {} } = ctx.request.body;
//
//       const results = {
//         main_categories: [],
//         categories: []
//       };
//
//       // Bulk update main categories
//       for (const documentId of Object.keys(main_categories)) {
//         const updateData = main_categories[documentId];
//         if (!updateData || typeof updateData !== 'object') {
//           results.main_categories.push({ documentId, success: false, error: 'Invalid update payload' });
//           continue;
//         }
//         const existing = await strapi.db.query('api::main-category.main-category').findOne({
//           where: { documentId }
//         });
//         if (!existing) {
//           results.main_categories.push({ documentId, success: false, error: 'Main category not found' });
//           continue;
//         }
//         const updated = await strapi.db.query('api::main-category.main-category').update({
//           where: { id: existing.id },
//           data: updateData
//         });
//         results.main_categories.push({ documentId, success: true, data: updated });
//       }
//
//       // Bulk update categories
//       for (const documentId of Object.keys(categories)) {
//         const updateData = categories[documentId];
//         if (!updateData || typeof updateData !== 'object') {
//           results.categories.push({ documentId, success: false, error: 'Invalid update payload' });
//           continue;
//         }
//         const existing = await strapi.db.query('api::product-category.product-category').findOne({
//           where: { documentId }
//         });
//         if (!existing) {
//           results.categories.push({ documentId, success: false, error: 'Category not found' });
//           continue;
//         }
//         const updated = await strapi.db.query('api::product-category.product-category').update({
//           where: { id: existing.id },
//           data: updateData
//         });
//         results.categories.push({ documentId, success: true, data: updated });
//       }
//
//       ctx.send({
//         summary: {
//           main_categories: {
//             total: results.main_categories.length,
//             successful: results.main_categories.filter(r => r.success).length,
//             failed: results.main_categories.filter(r => !r.success).length
//           },
//           categories: {
//             total: results.categories.length,
//             successful: results.categories.filter(r => r.success).length,
//             failed: results.categories.filter(r => !r.success).length
//           }
//         },
//         results
//       });
//     } catch (err) {
//       console.error('bulkUpdate error:', err);
//       return ctx.badRequest({ message: 'Error updating categories', error: err.message });
//     }
//   }
  };
});



// /**
//  * product-category controller
//  */
//
// import { factories } from '@strapi/strapi'
//
// export default factories.createCoreController('api::product-category.product-category');
