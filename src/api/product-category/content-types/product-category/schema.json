{"kind": "collectionType", "collectionName": "product_categories", "info": {"singularName": "product-category", "pluralName": "product-categories", "displayName": "Categories", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "name_ar": {"type": "string"}, "description": {"type": "text"}, "feature_image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "vendor": {"type": "relation", "relation": "manyToOne", "target": "api::vendor.vendor", "inversedBy": "categories"}, "products": {"type": "relation", "relation": "manyToMany", "target": "api::product.product", "private": true, "mappedBy": "categories"}, "main_category": {"type": "relation", "relation": "manyToOne", "target": "api::main-category.main-category", "inversedBy": "categories"}, "sort": {"type": "integer"}}}