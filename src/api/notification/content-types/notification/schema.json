{"kind": "collectionType", "collectionName": "notifications", "info": {"singularName": "notification", "pluralName": "notifications", "displayName": "Notifications", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "body": {"type": "string"}, "vendor": {"type": "relation", "relation": "manyToOne", "target": "api::vendor.vendor", "inversedBy": "notifications"}}}