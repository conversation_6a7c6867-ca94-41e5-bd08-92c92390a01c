import { factories } from '@strapi/strapi';
import { formatMedia } from '../../../utils/formatMedia';

export default factories.createCoreController('api::banner.banner', ({ strapi }) => {
  const formatBanner = (banner: any) => ({
    ...banner,
    media: formatMedia(banner.media), // Handling the `media` field
  });

  return {
    async find(ctx) {
      try {
        const sanitizedQueryParams = await this.sanitizeQuery(ctx);
        const { results, pagination } = await strapi.service('api::banner.banner').find(sanitizedQueryParams);

        const formattedResults = results.map(formatBanner);

        const sanitizedResults = await this.sanitizeOutput(formattedResults, ctx);
        return this.transformResponse(sanitizedResults, { pagination });
      } catch (err) {
        console.error('Error in find method:', err);
        return ctx.badRequest('Error fetching banners');
      }
    },

    async findOne(ctx) {
      try {
        const { id } = ctx.params;
        const sanitizedQueryParams = await this.sanitizeQuery(ctx);
        const result = await strapi.service('api::banner.banner').findOne(id, sanitizedQueryParams);

        const sanitizedResult = await this.sanitizeOutput(formatBanner(result), ctx);
        return this.transformResponse(sanitizedResult);
      } catch (err) {
        console.error('Error in findOne method:', err);
        return ctx.badRequest('Error fetching banner');
      }
    },
  };
});
