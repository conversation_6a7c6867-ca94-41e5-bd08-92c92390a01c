{"kind": "collectionType", "collectionName": "banners", "info": {"singularName": "banner", "pluralName": "banners", "displayName": "Banners", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "media": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "vendor": {"type": "relation", "relation": "manyToOne", "target": "api::vendor.vendor", "inversedBy": "banners"}}}