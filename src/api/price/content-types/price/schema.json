{"kind": "collectionType", "collectionName": "pricing", "info": {"singularName": "price", "pluralName": "pricing", "displayName": "Pricing", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "name_ar": {"type": "string"}, "description": {"type": "text"}, "description_ar": {"type": "text"}, "price": {"type": "decimal"}, "vendors": {"type": "relation", "relation": "oneToMany", "target": "api::vendor.vendor", "mappedBy": "pricing"}, "price_dollar": {"type": "decimal"}, "orders_count": {"type": "integer"}, "extra_order_price": {"type": "decimal"}, "sale_price": {"type": "decimal"}, "days": {"type": "integer"}}}