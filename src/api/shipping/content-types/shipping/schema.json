{"kind": "collectionType", "collectionName": "shippings", "info": {"singularName": "shipping", "pluralName": "shippings", "displayName": "Shippings", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "description": {"type": "text"}, "cost": {"type": "decimal"}, "cities_cost": {"displayName": "Cities Cost", "type": "component", "repeatable": true, "component": "cities-cost.cities-cost"}, "vendor": {"type": "relation", "relation": "manyToOne", "target": "api::vendor.vendor", "inversedBy": "shippings"}}}