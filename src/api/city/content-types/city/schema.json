{"kind": "collectionType", "collectionName": "cities", "info": {"singularName": "city", "pluralName": "cities", "displayName": "Cities", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "name_ar": {"type": "string"}, "country": {"type": "relation", "relation": "manyToOne", "target": "api::country.country", "inversedBy": "cities"}, "boundaries": {"type": "component", "component": "boundaries.boundaries", "repeatable": true}, "lat": {"type": "string"}, "lng": {"type": "string"}}}