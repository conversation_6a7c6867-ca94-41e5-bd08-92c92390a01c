// src/api/webhook/routes/webhook.ts

export default {
  routes: [
    {
      method: 'POST',
      path: '/webhooks', // Plural form
      handler: 'webhook.create',
      config: {
        auth: false,
        middlewares: [],
        policies: [],
      },
    },
    {
      method: 'GET',
      path: '/webhooks', // Plural form
      handler: 'webhook.find',
      config: {
        auth: false,
        middlewares: [],
        policies: [],
      },
    },
    {
      method: 'GET',
      path: '/webhooks/:id', // Plural form
      handler: 'webhook.findOne',
      config: {
        auth: false,
        middlewares: [],
        policies: [],
      },
    },
    {
      method: 'PUT',
      path: '/webhooks/:id', // Plural form
      handler: 'webhook.update',
      config: {
        auth: false,
        middlewares: [],
        policies: [],
      },
    },
    {
      method: 'DELETE',
      path: '/webhooks/:id', // Plural form
      handler: 'webhook.delete',
      config: {
        auth: false,
        middlewares: [],
        policies: [],
      },
    },
  ],
};


// /**
//  * webhook router
//  */
//
// import { factories } from '@strapi/strapi';
//
// export default factories.createCoreRouter('api::webhook.webhook');
