// src/api/webhook/controllers/webhook.ts

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::webhook.webhook', ({ strapi }) => ({
  // POST request (create)
  async create(ctx) {
    try {
      const { body } = ctx.request;

      // Create a new webhook entry directly from the body attributes
      const response = await strapi.entityService.create('api::webhook.webhook', {
        data: body,
      });

      ctx.send({ message: 'Webhook created successfully', data: response });
    } catch (error) {
      ctx.throw(400, 'Webhook creation failed');
    }
  },

  // GET request (find many)
  async find(ctx) {
    try {
      const response = await strapi.entityService.findMany('api::webhook.webhook', {
        ...ctx.query,
      });

      ctx.send({ message: 'Webhook data fetched successfully', data: response });
    } catch (error) {
      ctx.throw(400, 'Failed to fetch webhook data');
    }
  },

  // GET request (find one by id)
  async findOne(ctx) {
    try {
      const { id } = ctx.params;

      const response = await strapi.entityService.findOne('api::webhook.webhook', id);

      if (!response) {
        return ctx.notFound('Webhook not found');
      }

      ctx.send({ message: 'Webhook data fetched successfully', data: response });
    } catch (error) {
      ctx.throw(400, 'Failed to fetch webhook');
    }
  },

  // PUT request (update)
  async update(ctx) {
    try {
      const { id } = ctx.params;
      const { body } = ctx.request;

      // Update webhook entry by id with body attributes
      const response = await strapi.entityService.update('api::webhook.webhook', id, {
        data: body,
      });

      ctx.send({ message: 'Webhook updated successfully', data: response });
    } catch (error) {
      ctx.throw(400, 'Webhook update failed');
    }
  },

  // DELETE request (delete)
  async delete(ctx) {
    try {
      const { id } = ctx.params;

      // Delete the webhook entry by id
      const response = await strapi.entityService.delete('api::webhook.webhook', id);

      ctx.send({ message: 'Webhook deleted successfully', data: response });
    } catch (error) {
      ctx.throw(400, 'Webhook deletion failed');
    }
  },
}));



// /**
//  * webhook controller
//  */
//
// import { factories } from '@strapi/strapi'
//
// export default factories.createCoreController('api::webhook.webhook');
