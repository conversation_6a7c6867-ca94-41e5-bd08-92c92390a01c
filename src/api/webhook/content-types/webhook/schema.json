{"kind": "collectionType", "collectionName": "webhooks", "info": {"singularName": "webhook", "pluralName": "webhooks", "displayName": "Webhooks"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"api_key": {"type": "string"}, "invoice_key": {"type": "string"}, "invoice_id": {"type": "string"}, "payment_method": {"type": "string"}, "invoice_status": {"type": "string"}, "pay_load": {"type": "string"}, "referenceNumber": {"type": "string"}}}