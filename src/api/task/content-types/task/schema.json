{"kind": "collectionType", "collectionName": "tasks", "info": {"singularName": "task", "pluralName": "tasks", "displayName": "Tasks", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "description": {"type": "text"}, "is_done": {"type": "boolean", "default": false}, "attachment": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "vendor": {"type": "relation", "relation": "manyToOne", "target": "api::vendor.vendor", "inversedBy": "tasks"}}}