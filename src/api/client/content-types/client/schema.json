{"kind": "collectionType", "collectionName": "clients", "info": {"singularName": "client", "pluralName": "clients", "displayName": "Clients"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "phone_number": {"type": "string"}, "client_status": {"type": "enumeration", "enum": ["responded", "not_responded"]}, "statge": {"type": "enumeration", "enum": ["subscribed", "waiting", "not_interested", "call_again", "none"]}, "email": {"type": "string"}, "notes": {"type": "text"}, "vendor": {"type": "relation", "relation": "oneToOne", "target": "api::vendor.vendor", "inversedBy": "client"}}}