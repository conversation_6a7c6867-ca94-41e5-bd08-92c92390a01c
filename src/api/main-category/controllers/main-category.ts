
import { factories } from '@strapi/strapi';
import { formatMedia } from '../../../utils/formatMedia';

export default factories.createCoreController('api::main-category.main-category', ({ strapi }) => {
  // Format individual categories within the main category
  const formatSubCategory = (category: any) => {
    return {
      id: category.id,
      documentId: category.documentId,
      name: category.name,
      name_ar: category.name_ar,
      sort: category.sort,
      feature_image: formatMedia(category.feature_image),
    };
  };

  // Format main category response
  const formatCategory = (category: any) => {
    return {
      id: category.id,
      documentId: category.documentId,
      name: category.name,
      name_ar: category.name_ar,
      sort: category.sort,
      feature_image: formatMedia(category.feature_image),
      categories: category.categories ? category.categories.map(formatSubCategory) : undefined,
    };
  };

  return {
    async find(ctx) {
      try {
        const sanitizedQueryParams = await this.sanitizeQuery(ctx);
        const { results, pagination } = await strapi.service('api::main-category.main-category').find(sanitizedQueryParams);

        const formattedResults = results.map(formatCategory);

        const sanitizedResults = await this.sanitizeOutput(formattedResults, ctx);
        return this.transformResponse(sanitizedResults, { pagination });
      } catch (err) {
        console.error('Error in find method:', err);
        return ctx.badRequest('Error fetching product categories');
      }
    },

    async findOne(ctx) {
      try {
        const { id } = ctx.params;
        const sanitizedQueryParams = await this.sanitizeQuery(ctx);
        const result = await strapi.service('api::main-category.main-category').findOne(id, sanitizedQueryParams);

        const sanitizedResult = await this.sanitizeOutput(formatCategory(result), ctx);
        return this.transformResponse(sanitizedResult);
      } catch (err) {
        console.error('Error in findOne method:', err);
        return ctx.badRequest('Error fetching product category');
      }
    },

    async filteredMainCategories(ctx) {
      try {
        const sanitizedQueryParams = await this.sanitizeQuery(ctx);

        // Fetch main categories with their nested categories
        const { results: mainCategories, pagination } = await strapi.service('api::main-category.main-category').find(sanitizedQueryParams);

        // Fetch standalone categories (where main_category is null)
        const standaloneCategories = await strapi.entityService.findMany('api::product-category.product-category', {
          ...sanitizedQueryParams,
          filters: {
            ...(sanitizedQueryParams.filters as object || {}),
            main_category: null
          }
        });

        // Format main categories
        const formattedMainCategories = mainCategories.map(formatCategory);

        // Sort main categories by sort field (ascending)
        formattedMainCategories.sort((a, b) => (a.sort || 0) - (b.sort || 0));

        // Sort categories within each main category by sort field (ascending)
        formattedMainCategories.forEach(mainCategory => {
          if (mainCategory.categories && Array.isArray(mainCategory.categories)) {
            mainCategory.categories.sort((a, b) => (a.sort || 0) - (b.sort || 0));
          }
        });

        // Collect all category documentIds that are already in main categories
        const categoryDocumentIdsInMainCategories = new Set();
        formattedMainCategories.forEach(mainCategory => {
          if (mainCategory.categories) {
            mainCategory.categories.forEach(category => {
              categoryDocumentIdsInMainCategories.add(category.documentId);
            });
          }
        });

        // Filter standalone categories to exclude those already in main categories
        const filteredStandaloneCategories = standaloneCategories.filter(category =>
          !categoryDocumentIdsInMainCategories.has(category.documentId)
        );

        // Format standalone categories
        const formattedStandaloneCategories = filteredStandaloneCategories.map(formatSubCategory);

        // Sort standalone categories by sort field (ascending)
        formattedStandaloneCategories.sort((a, b) => (a.sort || 0) - (b.sort || 0));

        const response = {
          'main-categories': formattedMainCategories,
          'categories': formattedStandaloneCategories
        };

        const sanitizedResponse = await this.sanitizeOutput(response, ctx);
        return this.transformResponse(sanitizedResponse, { pagination });
      } catch (err) {
        console.error('Error in filteredMainCategories method:', err);
        }
    },


    async bulkUpdateMainCategories(ctx) {
      try {
        const { data } = ctx.request.body;
        if (!data || typeof data !== 'object') {
          return ctx.badRequest('Invalid request payload.');
        }

        const results = [];

        for (const documentId in data) {
          const payload = data[documentId];
          const existing = await strapi.db.query('api::main-category.main-category').findOne({
            where: { documentId },
          });

          if (!existing) {
            results.push({ documentId, success: false, error: 'Not found' });
            continue;
          }

          try {
            let updated = await strapi.entityService.update(
              'api::main-category.main-category',
              existing.id,
              {
                data: payload,
              }
            );

            // Auto-publish if not already published
            if (!updated.publishedAt) {
              updated = await strapi.entityService.update(
                'api::main-category.main-category',
                updated.id,
                { data: { publishedAt: new Date().toISOString() } }
              );
            }

            results.push({
              documentId,
              success: true,
              data: updated,
            });
          } catch (err) {
            results.push({
              documentId,
              success: false,
              error: err.message,
            });
          }
        }

        const successful = results.filter(r => r.success).length;
        const failed = results.length - successful;

        ctx.send({
          summary: { total: results.length, successful, failed },
          results,
        });
      } catch (err) {
        console.error(err);
        ctx.badRequest({ message: 'bulkUpdateMainCategories failed', error: err.message });
      }
    }
  };
});

