{"kind": "collectionType", "collectionName": "main_categories", "info": {"singularName": "main-category", "pluralName": "main-categories", "displayName": "Main Categories", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": false, "unique": false}, "name_ar": {"type": "string"}, "feature_image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "vendor": {"type": "relation", "relation": "manyToOne", "target": "api::vendor.vendor", "inversedBy": "main_categories"}, "categories": {"type": "relation", "relation": "oneToMany", "target": "api::product-category.product-category", "mappedBy": "main_category"}, "sort": {"type": "integer"}}}