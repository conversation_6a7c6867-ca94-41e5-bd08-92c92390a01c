/**
 * vendor controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::vendor.vendor', ({ strapi }) => ({
  async getVendorId(ctx) {
    try {
      const { documentId } = ctx.params;

      if (!documentId) {
        return ctx.badRequest('Document ID is required');
      }

      // Find vendor by documentId and only select the id field
      const vendor = await strapi.db.query('api::vendor.vendor').findOne({
        where: { documentId },
        select: ['id']
      });

      if (!vendor) {
        return ctx.notFound('Vendor not found');
      }

      const response = {
        id: vendor.id
      };

      return this.transformResponse(response);
    } catch (err) {
      console.error('Error in getVendorId method:', err);
      return ctx.badRequest('Error fetching vendor ID');
    }
  }
}));
