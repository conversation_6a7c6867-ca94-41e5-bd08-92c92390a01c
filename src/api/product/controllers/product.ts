import { factories } from '@strapi/strapi';
import { formatMedia } from '../../../utils/formatMedia';
import { Context } from 'koa';

export default factories.createCoreController('api::product.product', ({ strapi }) => {
  const formatProduct = (product: any) => ({
    ...product,
    images: formatMedia(product.images), // Handling the `images` field
  });

  return {
    async find(ctx) {
      try {
        const sanitizedQueryParams = await this.sanitizeQuery(ctx);
        const { results, pagination } = await strapi.service('api::product.product').find(sanitizedQueryParams);

        const formattedResults = results.map(formatProduct);

        const sanitizedResults = await this.sanitizeOutput(formattedResults, ctx);
        return this.transformResponse(sanitizedResults, { pagination });
      } catch (err) {
        console.error('Error in find method:', err);
        return ctx.badRequest('Error fetching products');
      }
    },

    async findOne(ctx) {
      try {
        const { id } = ctx.params;
        const sanitizedQueryParams = await this.sanitizeQuery(ctx);
        const result = await strapi.service('api::product.product').findOne(id, sanitizedQueryParams);

        const sanitizedResult = await this.sanitizeOutput(formatProduct(result), ctx);
        return this.transformResponse(sanitizedResult);
      } catch (err) {
        console.error('Error in findOne method:', err);
        return ctx.badRequest('Error fetching product');
      }
    },
  async bulkUpdate(ctx) {
    try {
      const { data } = ctx.request.body;
      if (!data || typeof data !== 'object') {
        return ctx.badRequest('Invalid request payload.');
      }

      const results = [];

      for (const documentId in data) {
        const payload = data[documentId];
        const existing = await strapi.db.query('api::product.product').findOne({
          where: { documentId },
        });

        if (!existing) {
          results.push({ documentId, success: false, error: 'Not found' });
          continue;
        }

        try {
          const updated = await strapi.entityService.update(
            'api::product.product',
            existing.id,
            {
              data: payload,
            }
          );

          results.push({
            documentId,
            success: true,
            data: formatProduct(updated),
          });
        } catch (err) {
          results.push({
            documentId,
            success: false,
            error: err.message,
          });
        }
      }

      const successful = results.filter(r => r.success).length;
      const failed = results.length - successful;
      ctx.send({ summary: { total: results.length, successful, failed }, results });
    } catch (err) {
      console.error(err);
      ctx.badRequest({ message: 'bulkUpdate failed', error: err.message });
    }
  }

  };
});
//     async bulkUpdate(ctx) {
//       try {
//         const { data } = ctx.request.body;
//         if (!data || typeof data !== 'object') {
//           return ctx.badRequest('Invalid request body. Expected "data" object with documentIds as keys.');
//         }
//
//         const documentIds = Object.keys(data);
//         if (documentIds.length === 0) {
//           return ctx.badRequest('No products to update. Provide at least one documentId.');
//         }
//
//         const results = [];
//
//         for (const documentId of documentIds) {
//           const updateData = data[documentId];
//
//           if (!updateData || typeof updateData !== 'object') {
//             results.push({ documentId, success: false, error: 'Invalid update payload' });
//             continue;
//           }
//
//           // Find the product row by documentId
//           const existing = await strapi.db.query('api::product.product').findOne({
//             where: { documentId }
//           });
//
//           if (!existing) {
//             results.push({ documentId, success: false, error: 'Product not found' });
//             continue;
//           }
//
//           // Update by primary key `id`
//           const updated = await strapi.db.query('api::product.product').update({
//             where: { id: existing.id },
//             data: updateData
//           });
//
//           results.push({
//             documentId,
//             success: true,
//             data: formatProduct(updated)
//           });
//         }
//
//         const successful = results.filter(r => r.success).length;
//         const failed = results.length - successful;
//
//         ctx.send({
//           summary: { total: results.length, successful, failed },
//           results
//         });
//       } catch (err) {
//         console.error('bulkUpdate error:', err);
//         return ctx.badRequest({ message: 'Error updating products', error: err.message });
//       }
//     }


// /**
//  * product controller
//  */
//
// import { factories } from '@strapi/strapi'
//
// export default factories.createCoreController('api::product.product');
