{"kind": "collectionType", "collectionName": "products", "info": {"singularName": "product", "pluralName": "products", "displayName": "Products", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "title_ar": {"type": "string"}, "description": {"type": "text"}, "description_ar": {"type": "text"}, "is_featured": {"type": "boolean", "default": false}, "is_active": {"type": "boolean", "default": true}, "is_sale": {"type": "boolean", "default": false}, "price": {"type": "decimal"}, "sale_price": {"type": "decimal"}, "quantity_sale": {"type": "decimal"}, "min_quantity_sale_number": {"type": "integer"}, "is_quantity_sale_percentage": {"type": "boolean"}, "inventory": {"type": "integer"}, "images": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "video": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "categories": {"type": "relation", "relation": "manyToMany", "target": "api::product-category.product-category", "inversedBy": "products"}, "vendor": {"type": "relation", "relation": "manyToOne", "target": "api::vendor.vendor", "inversedBy": "products"}, "sizes": {"type": "component", "component": "extra-settings.product-extra-settings", "repeatable": true}, "colors": {"type": "component", "component": "extra-settings.product-extra-settings", "repeatable": true}, "is_out_of_stock": {"type": "boolean", "default": false}, "inventory_enabled": {"type": "boolean", "default": true}, "is_size_color_inventory": {"type": "boolean", "default": false}, "sort": {"type": "integer"}}}