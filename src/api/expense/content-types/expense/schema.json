{"kind": "collectionType", "collectionName": "expenses", "info": {"singularName": "expense", "pluralName": "expenses", "displayName": "Expenses", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "price": {"type": "decimal"}, "is_paid": {"type": "boolean", "default": false}, "attachment": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "vendor": {"type": "relation", "relation": "manyToOne", "target": "api::vendor.vendor", "inversedBy": "expenses"}}}