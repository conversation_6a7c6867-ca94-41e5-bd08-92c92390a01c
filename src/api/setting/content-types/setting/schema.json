{"kind": "singleType", "collectionName": "settings", "info": {"singularName": "setting", "pluralName": "settings", "displayName": "Settings", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"version": {"type": "string"}, "check_update": {"type": "boolean", "default": true}, "terms_en": {"type": "richtext"}, "terms_ar": {"type": "richtext"}, "templates": {"type": "component", "component": "templates.templates", "repeatable": true}, "contact_us": {"type": "component", "component": "contact-us.contact-us", "repeatable": false}, "about_en": {"type": "richtext"}, "about_ar": {"type": "richtext"}, "projects": {"type": "component", "component": "templates.projects", "repeatable": true}, "roles": {"type": "component", "component": "permissions.roles", "repeatable": true}}}