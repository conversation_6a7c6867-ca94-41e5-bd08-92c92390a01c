{"kind": "collectionType", "collectionName": "orders", "info": {"singularName": "order", "pluralName": "orders", "displayName": "Orders", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"total": {"type": "decimal"}, "order_status": {"type": "enumeration", "default": "pending", "enum": ["pending", "confirmed", "delivering", "done", "canceled", "refunded"]}, "delivery_cost": {"type": "decimal"}, "note": {"type": "text"}, "from_store": {"type": "boolean", "default": false}, "order_id": {"type": "integer"}, "invoice_id": {"type": "integer"}, "guest_name": {"type": "string"}, "phone_number": {"type": "string"}, "vendor": {"type": "relation", "relation": "manyToOne", "target": "api::vendor.vendor", "inversedBy": "orders"}, "user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "orders"}, "payment": {"type": "relation", "relation": "oneToOne", "target": "api::payment.payment"}, "shipping": {"type": "relation", "relation": "oneToOne", "target": "api::shipping.shipping"}, "address": {"type": "component", "component": "address.address", "repeatable": false}, "products_quantity": {"type": "component", "component": "extra-settings.products-quantity", "repeatable": true}, "payment_attachment": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "promo_code": {"type": "relation", "relation": "oneToOne", "target": "api::promo-code.promo-code"}, "discount": {"type": "decimal"}, "location": {"type": "component", "component": "boundaries.boundaries", "repeatable": false}, "device_token": {"type": "string"}}}