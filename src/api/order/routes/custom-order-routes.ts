export default {
  routes: [
    {
      method: 'GET',
      path: '/orders-report',
      handler: 'order.ordersReport',
      config: {
        policies: [],
      },
    },
    {
      method: 'GET',
      path: '/orders-count',
      handler: 'order.ordersCount',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/orders-statistics',
      handler: 'order.statistics',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/orders-users',
      handler: 'order.orderUsers',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
