

import { factories } from '@strapi/strapi';
import { Context } from 'koa';

export default factories.createCoreController('api::order.order', ({ strapi }) => ({
   async find(ctx: Context) {
      try {
        const sanitizedQueryParams = await this.sanitizeQuery(ctx);

        const { results, pagination } = await strapi.service('api::order.order').find(sanitizedQueryParams);

        // Validate orders against webhook
        const validatedOrders = await Promise.all(
          results.map(async (order) => {
            if (order.invoice_id) {
              const webhookExists = await strapi.entityService.findMany('api::webhook.webhook', {
                filters: {
                  invoice_id: order.invoice_id.toString(),
                  invoice_status: 'paid',
                },
                limit: 1,
              });
              return webhookExists.length > 0 ? order : null;
            }
            return order;
          })
        );

        const filteredOrders = validatedOrders.filter(Boolean);

        // Ensure uniqueness of orders
        const uniqueOrdersMap = new Map<number, any>();
        filteredOrders.forEach((order) => {
          if (!uniqueOrdersMap.has(order.id)) {
            uniqueOrdersMap.set(order.id, order);
          }
        });

        const finalOrders = Array.from(uniqueOrdersMap.values());

        // Handle pagination properly
        const sanitizedResults = await this.sanitizeOutput(finalOrders, ctx);
        return this.transformResponse(sanitizedResults, { pagination });
      } catch (err) {
        console.error('Error in find method:', err);
        return ctx.badRequest('Error fetching orders');
      }
    },


  async ordersReport(ctx: Context) {
    try {
      const { query } = ctx;
      const filters = query.filters || {};
      const sortArray = Array.isArray(query.sort) ? query.sort : [query.sort];
      const formattedSort = sortArray.map((s: string) => {
        const [field, order] = s.split(':');
        return { [field]: order };
      });

      const orders = await strapi.entityService.findMany('api::order.order', {
        filters,
        limit: 200000,
        sort: formattedSort,
        populate: ['vendor'],
      });

      const validatedOrders = await Promise.all(
        orders.map(async (order) => {
          if (order.invoice_id) {
            const webhook = await strapi.entityService.findMany('api::webhook.webhook', {
              filters: {
                invoice_id: order.invoice_id.toString(),
                invoice_status: 'paid',
              },
              limit: 1,
            });
            return webhook.length > 0 ? order : null;
          }
          return order;
        })
      );

      const filteredOrders = validatedOrders.filter(Boolean);

      const uniqueOrdersMap = new Map<number, any>();
      filteredOrders.forEach((order) => {
        const existingOrder = uniqueOrdersMap.get(order.invoice_id);
        if (existingOrder) {
          if (
            existingOrder.order_status === 'pending' &&
            order.order_status !== 'pending'
          ) {
            uniqueOrdersMap.set(order.invoice_id, order);
          } else if (
            existingOrder.order_status === 'pending' &&
            order.order_status === 'pending'
          ) {
            if (new Date(order.createdAt) > new Date(existingOrder.createdAt)) {
              uniqueOrdersMap.set(order.invoice_id, order);
            }
          }
        } else {
          uniqueOrdersMap.set(order.invoice_id, order);
        }
      });

      const finalOrders = Array.from(uniqueOrdersMap.values()).concat(
        orders.filter(order => !order.invoice_id)
      );

      const earningsByDate: { [key: string]: number } = {};
      let totalEarnings = 0;

      finalOrders.forEach((order) => {
        const createdAtDate = new Date(order.createdAt).toISOString().split('T')[0];
        if (!earningsByDate[createdAtDate]) {
          earningsByDate[createdAtDate] = 0;
        }
        earningsByDate[createdAtDate] += order.total;
        totalEarnings += order.total;
      });

      const dailyEarnings = Object.entries(earningsByDate).map(([date, earnings]) => ({
        date,
        earnings,
      }));

      ctx.send({
        total_count: finalOrders.length,
        total_earnings: totalEarnings,
        daily_earnings: dailyEarnings,
      });
    } catch (err) {
      console.error('Error in ordersReport method:', err);
      ctx.badRequest({ message: 'Error generating report', error: err });
    }
  },

  async ordersCount(ctx: Context) {
    try {
      const { query } = ctx;
      const filters = query.filters || {};
      const statusFilter = {
        $and: [
          filters,
          {
            order_status: {
              $in: ['pending', 'delivering', 'confirmed', 'done'],
            },
          },
        ],
      };

      const orders = await strapi.entityService.findMany('api::order.order', {
        filters: statusFilter,
        limit: 200000,
      });

      const validatedOrders = await Promise.all(
        orders.map(async (order) => {
          if (order.invoice_id) {
            const webhook = await strapi.entityService.findMany('api::webhook.webhook', {
              filters: {
                invoice_id: order.invoice_id.toString(),
                invoice_status: 'paid',
              },
              limit: 1,
            });
            return webhook.length > 0 ? order : null;
          }
          return order;
        })
      );

      const filteredOrders = validatedOrders.filter(Boolean);
      const uniqueOrdersMap = new Map<number, any>();
      filteredOrders.forEach((order) => {
        const existingOrder = uniqueOrdersMap.get(order.invoice_id);
        if (existingOrder) {
          if (
            existingOrder.order_status === 'pending' &&
            order.order_status !== 'pending'
          ) {
            uniqueOrdersMap.set(order.invoice_id, order);
          } else if (
            existingOrder.order_status === 'pending' &&
            order.order_status === 'pending'
          ) {
            if (new Date(order.createdAt) > new Date(existingOrder.createdAt)) {
              uniqueOrdersMap.set(order.invoice_id, order);
            }
          }
        } else {
          uniqueOrdersMap.set(order.invoice_id, order);
        }
      });

      const finalOrders = Array.from(uniqueOrdersMap.values()).concat(
        orders.filter(order => !order.invoice_id)
      );

      ctx.send({ count: finalOrders.length });
    } catch (err) {
      console.error('Error in ordersCount method:', err);
      ctx.badRequest({ message: 'Error counting orders', error: err });
    }
  },

  async statistics(ctx: Context) {
    try {
      const { query } = ctx;
      const filters = query.filters || {};

      const orders = await strapi.entityService.findMany('api::order.order', {
        filters,
        limit: 200000,
        populate: ['vendor'],
      });

      const validatedOrders = await Promise.all(
        orders.map(async (order) => {
          if (order.invoice_id) {
            const webhook = await strapi.entityService.findMany('api::webhook.webhook', {
              filters: {
                invoice_id: order.invoice_id.toString(),
                invoice_status: 'paid',
              },
              limit: 1,
            });
            return webhook.length > 0 ? order : null;
          }
          return order;
        })
      );

      const filteredOrders = validatedOrders.filter(Boolean);

      const uniqueOrdersMap = new Map<number, any>();
      filteredOrders.forEach((order) => {
        const existingOrder = uniqueOrdersMap.get(order.invoice_id);
        if (existingOrder) {
          if (
            existingOrder.order_status === 'pending' &&
            order.order_status !== 'pending'
          ) {
            uniqueOrdersMap.set(order.invoice_id, order);
          } else if (
            existingOrder.order_status === 'pending' &&
            order.order_status === 'pending'
          ) {
            if (new Date(order.createdAt) > new Date(existingOrder.createdAt)) {
              uniqueOrdersMap.set(order.invoice_id, order);
            }
          }
        } else {
          uniqueOrdersMap.set(order.invoice_id, order);
        }
      });

      const finalOrders = Array.from(uniqueOrdersMap.values()).concat(
        orders.filter(order => !order.invoice_id)
      );

      // Calculate statistics
      const today = new Date().toISOString().split('T')[0];
      let totalEarnings = 0;
      let dailyOrdersCount = 0;
      let dailyEarnings = 0;

      finalOrders.forEach((order) => {
        const orderDate = new Date(order.createdAt).toISOString().split('T')[0];
        totalEarnings += order.total || 0;

        if (orderDate === today) {
          dailyOrdersCount++;
          dailyEarnings += order.total || 0;
        }
      });

      ctx.send({
        total_orders: finalOrders.length,
        total_earnings: parseFloat(totalEarnings.toFixed(2)),
        daily_orders: dailyOrdersCount,
        daily_earnings: parseFloat(dailyEarnings.toFixed(2)),
      });
    } catch (err) {
      console.error('Error in statistics method:', err);
      ctx.badRequest({ message: 'Error generating statistics', error: err });
    }
  },

  async orderUsers(ctx: Context) {
    try {
      const { query } = ctx;
      const filters = query.filters || {};

      const orders = await strapi.entityService.findMany('api::order.order', {
        filters,
        limit: 200000,
        populate: ['user'],
      });

      const validatedOrders = await Promise.all(
        orders.map(async (order) => {
          if (order.invoice_id) {
            const webhook = await strapi.entityService.findMany('api::webhook.webhook', {
              filters: {
                invoice_id: order.invoice_id.toString(),
                invoice_status: 'paid',
              },
              limit: 1,
            });
            return webhook.length > 0 ? order : null;
          }
          return order;
        })
      );

      const filteredOrders = validatedOrders.filter(Boolean);

      const uniqueOrdersMap = new Map<number, any>();
      filteredOrders.forEach((order) => {
        const existingOrder = uniqueOrdersMap.get(order.invoice_id);
        if (existingOrder) {
          if (
            existingOrder.order_status === 'pending' &&
            order.order_status !== 'pending'
          ) {
            uniqueOrdersMap.set(order.invoice_id, order);
          } else if (
            existingOrder.order_status === 'pending' &&
            order.order_status === 'pending'
          ) {
            if (new Date(order.createdAt) > new Date(existingOrder.createdAt)) {
              uniqueOrdersMap.set(order.invoice_id, order);
            }
          }
        } else {
          uniqueOrdersMap.set(order.invoice_id, order);
        }
      });

      const finalOrders = Array.from(uniqueOrdersMap.values()).concat(
        orders.filter(order => !order.invoice_id)
      );

      // Group orders by user ID first, then by phone number
      const userGroups = new Map<string, any>();

      finalOrders.forEach((order) => {
        let groupKey: string;
        let groupData: any;

        if (order.user && order.user.id) {
          // Group by user ID
          groupKey = `user_${order.user.id}`;
          if (!userGroups.has(groupKey)) {
            groupData = {
              guest_name: order.guest_name || null,
              phone_number: order.phone_number || null,
              device_token: order.user.device_token || null,
              user: order.user,
              total_orders: 0,
              canceled_orders: 0,
              refunded_orders: 0,
            };
            userGroups.set(groupKey, groupData);
          } else {
            groupData = userGroups.get(groupKey);
          }
        } else {
          // Group by phone number for guest orders
          groupKey = `phone_${order.phone_number || 'unknown'}`;
          if (!userGroups.has(groupKey)) {
            groupData = {
              guest_name: order.guest_name || null,
              phone_number: order.phone_number || null,
              device_token: null,
              user: null,
              total_orders: 0,
              canceled_orders: 0,
              refunded_orders: 0,
            };
            userGroups.set(groupKey, groupData);
          } else {
            groupData = userGroups.get(groupKey);
          }
        }

        // Count orders by status
        groupData.total_orders++;
        if (order.order_status === 'canceled') {
          groupData.canceled_orders++;
        }
        if (order.order_status === 'refunded') {
          groupData.refunded_orders++;
        }
      });

      const result = Array.from(userGroups.values());

      ctx.send(result);
    } catch (err) {
      console.error('Error in orderUsers method:', err);
      ctx.badRequest({ message: 'Error generating order users data', error: err });
    }
  },
}));
