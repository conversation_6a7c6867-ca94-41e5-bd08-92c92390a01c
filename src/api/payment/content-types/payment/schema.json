{"kind": "collectionType", "collectionName": "payments", "info": {"singularName": "payment", "pluralName": "payments", "displayName": "Payments", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "description": {"type": "text"}, "vendor": {"type": "relation", "relation": "manyToOne", "target": "api::vendor.vendor", "inversedBy": "payments"}, "api_key": {"type": "string"}}}