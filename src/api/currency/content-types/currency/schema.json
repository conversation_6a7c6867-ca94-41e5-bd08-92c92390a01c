{"kind": "collectionType", "collectionName": "currencies", "info": {"singularName": "currency", "pluralName": "currencies", "displayName": "Currencies", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"currency_en": {"type": "string"}, "currency_ar": {"type": "string"}, "symbol": {"type": "string"}, "configs": {"type": "relation", "relation": "manyToMany", "target": "api::config.config", "inversedBy": "currencies"}}}